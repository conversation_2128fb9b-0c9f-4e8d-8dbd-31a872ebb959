<template>
  <el-header class="top-0 !h-[146.5px] w-full flex" :class="props.showHeadBtnGroup ? 'justify-center' : 'justify-start'">
    <!-- 左 -->
    <div class="relative w-[512px]">
      <div class="bf-logo absolute top-4 left-16 ml-[10px] w-[294px] h-[111px]" />
    </div>

    <!-- 中 -->
    <div class="w-[896px] text-white text-center flex flex-col align-center">
      <EllipsisText
        class="you-she-biao-ti-hei text-[44px] m-0 !w-[644px] !h-[50px] !leading-[50px]"
        :content="defaultTitle"
        style="background: linear-gradient(to bottom, #ffffff 32%, #245398 100%); -webkit-background-clip: text; background-clip: text; color: transparent"
      />
      <EllipsisText class="tracking-[2px] !w-[644px]" :content="'Smart Digital Connection System Platform'" />
      <div class="head-menu w-[777px] h-[50.5px] flex justify-center gap-2" v-if="props.showHeadMenu">
        <div class="cursor-pointer menu-item menu-selected you-she-biao-ti-hei text-[21px] w-[152px] h-[31.5px] mt-[6px]">管理平台</div>
      </div>
    </div>

    <!-- 右 -->
    <div class="w-[512px] text-white flex justify-start gap-12" v-if="props.showHeadBtnGroup">
      <div class="w-[124px] h-[44px] mt-[40px] ml-[30px] flex align-center gap-4 border-r-1 border-[#2A7CAE]">
        <img :src="serverStatusImg" class="!w-6 !h-6" />

        <div class="!h-[44px] flex flex-col items-end text-[14px]">
          <!-- TODO 获取天气信息应该从服务器获取 -->
          <p class="m-0">多云转晴</p>
          <p class="m-0">
            {{ todayInChineseManual }}
          </p>
        </div>
      </div>
      <div class="w-[289px] h-[44px] mt-[40px] flex align-center gap-4">
        <SwitchLangs class="!w-[36px] !h-[36px]" />

        <div class="cursor-pointer w-[36px] h-[36px]" @click="openSettingsDialog">
          <img src="@/assets/images/common/setting_btn.svg" />
        </div>

        <div class="flex items-center gap-2 text-[14px]">
          <img :src="serverStatusImg" class="!w-6 !h-6" />
          <div class="!h-[44px] flex flex-col text-[14px]">
            <p class="m-0">{{ userTitle }}： {{ userName }}</p>
            <p class="m-0">
              {{ currentTime }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <settingsComp v-if="settingsCompLoaded" v-model="settingsCompVisible" />
  </el-header>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted, defineAsyncComponent } from 'vue'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import SwitchLangs from '@/components/common/switchLangs.vue'
  import serverStatusReady from '@/assets/images/loginBg/server_status_ready.svg'
  import serverStatus from '@/assets/images/loginBg/server_status.svg'
  import { useRoute } from 'vue-router'
  import { DateMask, TimeMask, nowLocalTime } from '@/utils/time.js'
  import dayjs from 'dayjs'
  import { useI18n } from 'vue-i18n'

  const { t } = useI18n()
  const route = useRoute()
  const firstLevelRoute = route.path.split('/')[1]

  const userTitle = computed(() => {
    return firstLevelRoute === 'dataManage' ? '管理员' : '调度员'
  })

  const userName = computed(() => {
    return bfglob.userInfo?.name
  })

  const todayInChineseManual = computed(() => {
    const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    const dayIndex = dayjs().day()
    return t(`dialog.${weekdays[dayIndex]}`)
  })

  const props = defineProps({
    showHeadMenu: {
      type: Boolean,
      default: true,
    },
    showHeadBtnGroup: {
      type: Boolean,
      default: true,
    },
  })

  const defaultTitle = computed(() => {
    return bfglob.siteConfig.moveTitle || t('header.moveTitle')
  })

  const connectStats = ref(1001)
  const serverStatusImg = computed(() => {
    return connectStats.value === 1001 ? serverStatusReady : serverStatus
  })

  const settingsCompVisible = ref(false)
  const settingsCompLoaded = ref(false)
  const settingsComp = defineAsyncComponent(() => import('@/components/dialogs/userSetting.vue'))
  const openSettingsDialog = () => {
    settingsCompLoaded.value = true
    settingsCompVisible.value = true
  }

  const currentTime = ref('')
  const updateTime = () => {
    currentTime.value = nowLocalTime(TimeMask + '  ' + DateMask)
  }
  let dateUpdateTimer = null

  onMounted(() => {
    updateTime()
    dateUpdateTimer = setInterval(updateTime, 1000)
    // if (navigator.geolocation) {
    //   navigator.geolocation.getCurrentPosition(
    //       async (position) => {
    //         const latitude = position.coords.latitude   // 纬度
    //         const longitude = position.coords.longitude // 经度
    //
    //         console.log(`你的位置是：纬度 ${latitude}, 经度 ${longitude}`)
    //         const apiKey = 'd55b25d90048be75124487a07ffc5d9e'
    //         const url = `https://api.openweathermap.org/data/2.5/weather?lat=${latitude}&lon=${longitude}&appid=${apiKey}&units=metric`
    //
    //         const res = await fetch(url)
    //         const data = await res.json()
    //
    //         console.log('天气数据：', data)
    //       },
    //       (error) => {
    //         console.error("获取地理位置失败：", error.message)
    //       }
    //   )
    // } else {
    //   console.error("你的浏览器不支持地理位置服务。")
    // }
  })

  onUnmounted(() => {
    if (dateUpdateTimer) {
      clearInterval(dateUpdateTimer)
    }
  })
</script>

<style scoped lang="scss">
  .head-menu {
    background: url('@/assets/images/common/1x/head_nav.webp') no-repeat center;
    background-size: 100% 100%;

    .menu-item {
    }

    .menu-selected {
      background: url('@/assets/images/common/1x/nav_selected.webp') no-repeat center;
      background-size: 100% 100%;
    }
  }

  @media screen and (min-width: 2561px) {
    .head-menu {
      background: url('@/assets/images/common/2x/head_nav.webp') no-repeat center;
      background-size: 100% 100%;
    }

    .menu-item {
    }

    .menu-selected {
      background: url('@/assets/images/common/2x/nav_selected.webp') no-repeat center;
      background-size: 100% 100%;
    }
  }
</style>
