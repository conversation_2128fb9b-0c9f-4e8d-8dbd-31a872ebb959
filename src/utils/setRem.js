const designWidth = 1920 // 设计基准1920 默认在1920*1080分辨率下设计

// src/utils/setRem.ts
const setRem = () => {
  const baseFontSize = 16 // 1rem = 16px
  const clientWidth = document.documentElement.clientWidth
  const html = document.documentElement
  const scale = clientWidth / designWidth
  html.style.fontSize = baseFontSize * scale + 'px'
}

window.addEventListener('resize', setRem)
window.addEventListener('DOMContentLoaded', setRem)

setRem()

/**
 * 将px转换为rem
 * @param {Number} px
 * @returns {String} 转换后的rem值
 */
export const convertPxToRem = px => {
  const baseFontSizeStr = document.documentElement.style.getPropertyValue('font-size')
  const baseFontSize = parseFloat(baseFontSizeStr) || 16
  const precision = 5 // 保留小数点后5位
  const multiplier = Math.pow(10, precision + 1),
    wholeNumber = Math.floor((px / baseFontSize) * multiplier)
  return (Math.round(wholeNumber / 10) * 10) / multiplier
}

/**
 * 计算缩放后的尺寸
 * @param {Number} DesignSize 设计尺寸，单位为px
 * @description 传入设计尺寸，返回缩放后的尺寸
 * @example calcScaleSize(100) // 返回 100px 在当前屏幕下的缩放尺寸
 * @returns {Number} 缩放后的尺寸，单位为px
 */
export const calcScaleSize = designSize => {
  const clientWidth = document.documentElement.clientWidth
  const scale = clientWidth / designWidth
  return designSize * scale
}
