@use '@/assets/fonts/fonts.css' as *;

.el-select.bf-select {
  @function _px($n) {
    @return #{$n}px;
  }

  .el-select__wrapper {
    background-color: transparent;
    border-radius: unset;
    border: 1px solid #94CCE8;
    color: #fff;

    .el-select__placeholder {
      color: #fff;
      opacity: 0.6;
    }

    .el-select__selected-item {
      color: #fff;
    }

    .el-select__input {
      color: #fff;
    }

    .el-select__caret {
      color: #94CCE8;
    }

    .el-select__suffix {
      color: #94CCE8;
    }

    &:hover {
      border-color: #94CCE8;
    }

    &.is-focused {
      border-color: #94CCE8;
    }
  }

  &.is-disabled {
    .el-select__wrapper {
      background-color: rgba(148, 204, 232, 0.1);
      border-color: rgba(148, 204, 232, 0.3);
      color: rgba(255, 255, 255, 0.3);

      .el-select__placeholder {
        color: rgba(255, 255, 255, 0.3);
      }

      .el-select__selected-item {
        color: rgba(255, 255, 255, 0.3);
      }

      .el-select__caret {
        color: rgba(148, 204, 232, 0.3);
      }
    }
  }
}

// 下拉面板样式
.el-select-dropdown {
  background-color: rgba(0, 0, 0, 0.8);
  border: 1px solid #94CCE8;
  border-radius: 4px;

  .el-select-dropdown__item {
    color: #fff;
    background-color: transparent;

    &:hover {
      background-color: rgba(148, 204, 232, 0.2);
    }

    &.is-selected {
      background-color: rgba(148, 204, 232, 0.3);
      color: #fff;
    }

    &.is-disabled {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  .el-select-dropdown__empty {
    color: rgba(255, 255, 255, 0.6);
  }
}
