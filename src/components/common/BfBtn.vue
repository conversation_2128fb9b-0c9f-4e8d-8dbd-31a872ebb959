<!-- ClippedButton.vue -->
<template>
  <div class="clipped-el-button-wrapper" :style="componentStyle">
    <i class="corner-triangle top-left"></i>
    <i class="corner-triangle bottom-right"></i>
    <el-button v-bind="$attrs">
      <slot />
    </el-button>
  </div>
</template>

<script setup>
  import { computed } from 'vue'

  const props = defineProps({
    borderWidth: { type: Number, default: 1 },
    width: { type: String, default: 'auto' },
    height: { type: String, default: 'auto' },
    fontSize: { type: String, default: '32px' },
    backgroundColor: { type: String, default: '#7575752B' },
    color: { type: String, default: '#FF4A4A' },
    boxShadow: { type: String, default: '0 4px 8px rgba(0, 0, 0, 0.2)' },
  })

  const componentStyle = computed(() => {
    const clipPathValue = `polygon(
      8px 0,                       /* 左上角切角 */
      100% 0,
      100% calc(100% - 11px),      /* 右下角切角 */
      calc(100% - 11px) 100%,      /* 右下角切角 */
      0 100%,
      0 8px                        /* 左上角切角 */
    )`

    return {
      '--button-width': props.width,
      '--button-height': props.height,
      '--font-size': props.fontSize,
      '--background-color': props.backgroundColor,
      '--color': props.color,
      '--clip-path-value': clipPathValue,
      '--border-width': `${props.borderWidth}px`,
      '--box-shadow': props.boxShadow,
    }
  })
</script>

<style scoped>
  /* 1. 容器 */
  .clipped-el-button-wrapper {
    position: relative;
    display: inline-block;
    width: var(--button-width);
    height: var(--button-height);
    isolation: isolate;
    transition:
      transform 0.2s ease,
      filter 0.3s ease,
      box-shadow 0.3s ease;
  }

  .clipped-el-button-wrapper:active {
    transform: scale(0.98);
  }

  /* 2. 底层：渐变边框 */
  .clipped-el-button-wrapper::before {
    content: '';
    position: absolute;
    inset: 0;
    z-index: -2;
    transition: filter 0.3s ease;
    clip-path: var(--clip-path-value);
    /* 【修正】将负责绘制边框的背景渐变加回来 */
    /* background: linear-gradient(to bottom, transparent, var(--color)); */
  }

  /* 3. 中层：背景 */
  .clipped-el-button-wrapper::after {
    content: '';
    position: absolute;
    inset: var(--border-width);
    background: var(--background-color);
    z-index: 1;
    clip-path: var(--clip-path-value);
    /* 您新增的 box-shadow 放在这里是正确的，它会被 clip-path 裁剪 */
    box-shadow: var(--box-shadow);
    backdrop-filter: brightness(1);
  }

  .corner-triangle {
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: var(--color);
    z-index: 1;
    transition: filter 0.3s ease;
  }

  .top-left {
    top: 0;
    left: 0;
    clip-path: polygon(0 0, 100% 0, 0 100%);
  }

  .bottom-right {
    bottom: 0;
    right: 0;
    clip-path: polygon(100% 100%, 100% 0, 0 100%);
  }

  /* 【修改】Hover效果现在需要作用于 ::after 上的阴影和 ::before 上的边框 */
  .clipped-el-button-wrapper:hover::before,
  .clipped-el-button-wrapper:hover .corner-triangle {
    filter: brightness(1.2);
  }

  .clipped-el-button-wrapper:hover::after {
    filter: brightness(1.1); /* 给阴影也加一点悬浮效果 */
  }

  /* 5. 内部的 el-button */
  .clipped-el-button-wrapper :deep(.el-button) {
    background: transparent !important;
    border: none !important;
    color: var(--color) !important;
    font-size: var(--font-size) !important;
    font-family: 'Heiti SC', 'Microsoft YaHei', sans-serif !important;
    font-weight: bold !important;
    width: 100%;
    height: 100%;
    padding: 12px 40px;
  }

  .clipped-el-button-wrapper :deep(.el-button:hover),
  .clipped-el-button-wrapper :deep(.el-button:focus) {
    background-color: transparent !important;
    border-color: transparent !important;
    color: var(--color) !important;
  }
</style>
