<template>
  <div
    class="cursor-pointer relative isolate flex justify-center items-center h-[28px] p-[5px] rounded-[2px]! bg-gradient-to-r from-[#0e85ba] to-[#001024] after:content-[''] after:-z-1 after:absolute after:top-0 after:left-0 after:right-0 after:bottom-[1px] after:pointer-events-none after:bg-[#104cba] after:rounded-[2px] hover:after:bg-[#508EFF] focus:opacity-70 active:opacity-70 transition duration-100 ease-in-out flex-auto"
    :class="{ disabled: !enable }"
  >
    <img v-if="iconSrc.length > 0" :src="iconSrc" class="relative size-[17px] aspect-square" />
    <data-table-icon v-if="iconFont.length > 0" :icon="iconFont" />
    <div
      class="relative text-[13px] text-white font-medium text-center align-middle h-[22px] ml-[5px] py-[2px] px-[9px] bg-[#000]/30 flex items-center justify-center w-full"
    >
      <slot></slot>
      <div class="absolute size-[1.5px] bg-sky-100/70 top-0 left-0"></div>
      <div class="absolute size-[1.5px] bg-sky-100/70 top-0 right-0"></div>
      <div class="absolute size-[1.5px] bg-sky-100/70 bottom-0 right-0"></div>
      <div class="absolute size-[1.5px] bg-sky-100/70 bottom-0 left-0"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  const {
    iconSrc = '',
    iconFont = '',
    enable = true,
  } = defineProps<{
    // 图标的URL,用于img标签
    iconSrc?: string
    // 图标字体类名,用于iconfont图标
    iconFont?: string
    enable?: boolean
  }>()
</script>

<style lang="scss" scoped>
  .disabled {
    pointer-events: none;
    opacity: 0.5;
  }
</style>
