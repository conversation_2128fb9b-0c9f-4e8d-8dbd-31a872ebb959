import globals from 'globals'
import pluginJs from '@eslint/js'
import pluginVue from 'eslint-plugin-vue'
import tseslint from 'typescript-eslint'
import eslintConfigPrettier from 'eslint-config-prettier'

/** @type {import('eslint').Linter.Config[]} */
export default tseslint.config(
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        bfglob: 'writable', // 'readonly' 或 'writable', 取决于是否需要在代码中修改 bfglob
        window: 'writable', // 通常也需要声明 window
        document: 'writable', // 以及 document
        $: 'writable',
        jQuery: 'writable',
        CLIENT_BUILD_TIME: 'readable',
        CLIENT_GIT_TAG: 'readable',

        // 忽略element-plus自动导入的ElMessage等模块引起的'node-undef'错误
        ElMessage: 'readonly',
        ElMessageBox: 'readonly',
        ElNotification: 'readonly',
        ElLoading: 'readonly',
      },
    },
  },
  {
    ignores: [
      '.gitignore',
      'build/',
      'coverage/',
      'src/css/iconfont/*',
      'src/writingFrequency/modelInfo.js',
      'src/components/common/tableTree/plugins/*',
      'src/modules/protocol/gen/*',
      'src/assets/weathIconfont/',
      'src/assets/bfdxFont/',
    ],
  },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  ...pluginVue.configs['flat/recommended'],
  {
    // Apply type-aware linting only to source files
    files: ['**/*.{ts,tsx,vue}'],
    languageOptions: {
      parserOptions: {
        project: true,
        tsconfigRootDir: import.meta.dirname,
        extraFileExtensions: ['.vue'],
        parser: tseslint.parser,
      },
    },
  },
  {
    rules: {
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': [
        'warn', // 或者 "error"
        {
          argsIgnorePattern: '^_', // 忽略所有以 '_' 开头的参数
          varsIgnorePattern: '^_', // 忽略所有以 '_' 开头的变量
          caughtErrorsIgnorePattern: '^_', // 忽略所有以 '_' 开头的 catch 错误变量
        },
      ],
      '@typescript-eslint/no-unused-expressions': 'off',
      '@typescript-eslint/no-this-alias': 'off',
      'no-case-declarations': 'off',

      'vue/no-v-text-v-html-on-component': 'off',
      'vue/no-v-html': 'off',
      'vue/require-default-prop': 'off',
      'vue/order-in-components': 'off',
      'vue/attribute-hyphenation': 'off',
      'vue/require-prop-type-constructor': 'off',
      'vue/one-component-per-file': 'off',
      'vue/attributes-order': 'off',
    },
  },
  eslintConfigPrettier
)
