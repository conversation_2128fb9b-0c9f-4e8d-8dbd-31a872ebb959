{"auth": {"applyAuth": "Apply License", "authDescription": "License description", "authExpiredAlert": "The license information has expired, please re-apply for license!", "authFileError": "License file error", "authorizedContent": "License content", "authorizedModules": "<PERSON><PERSON><PERSON>", "authorizedNumber": "Number", "authorizedTime": "Authorized time", "expireTime": "Expire Time", "importAuthFile": "Import license file", "logout": "Logout", "modules": {"controller": "Controller", "device": "<PERSON><PERSON>", "max-controllers": "Number of controllers", "max-devices": "Number of terminals", "max-users": "Number of users", "mod-dispatch": "Network dispatch", "mod-phone-gateway": "Phone Gateway", "mod-record": "Recording function", "mod-rfid": "Patrol function", "mod-svt": "Virtual cluster access", "mod-traditional-dmr": "Conventional DMR terminal access", "user": "User"}, "noAuthAlert": "No license information, please apply for license first!", "noReminderToday": "No reminder today", "noSpecifiedModuleAuth": "No ({module}) license info, please apply for license first!", "permanent": "Permanent license", "projName": "Project name", "queryAuthFailed": "Failed to query license information.", "reapplyAuthTips": "There are {days} days before the license expires, do you want to reapply for license?", "remainingExpirationTime": "({days} days)", "requestAuthSuccess": "Request License Successful", "unlimited": "unlimited"}, "dataTable": {"BSGroupCall_2": "Level 2 devices", "BSGroupCall_23": "Level 2&3 devices", "BSGroupCall_3": "Level 3 devices", "IPAddress": "IP Address", "PTY": "Percent of pass", "acPowerOffAlarm": "AC power off alarm", "alarmConditions": "Recevied alarm status", "alarmDevice": "Alarm device", "alarmTime": "Alarm time", "alarmType": "Alarm type", "alarmUserName": "Alarm person", "allChannel": "All Channel", "allDevices": "All devices", "allSys": "System All Call", "antiDismantlingAlarm": "Anti-dismantling alarm", "backToWorkPrompt": "Back to work prompt", "batteryOn": "Power on by battery", "callChannel": "Calling channel", "callTarget": "Call target", "callTime": "Calling time", "checkCount": "Amounts need to be checked", "checkDevice": "Checking devices", "checkResult": "Checking result", "checkTime": "Checking time", "checkTimeEarly": "The earliest checking time", "checkTimeEnd": "The latest checking time", "checkUser": "Checking person", "confirmTime": "Confirm the time", "ctrlAlarm": "<PERSON><PERSON>", "ctrlDataTime": "Data Time", "departureAlarm": "Departure alarm", "deviceParentName": "Unit of terminal", "direction": "Direction", "dispatchTarget": "Dispatching target", "dispatchTime": "Dispatching Time", "dispatchTypeName": "Dispatching Type", "duration": "Duration(Sec)", "emergency": "Emergency alarm", "endRoomOrgName": "Organization which end metting", "endRoomPerson": "Person who end metting", "endWork": "Off duty", "eventType": "Event type", "exportExcel": "Export Excel", "fail": "Unqualified", "fanExeception": "Fan exception", "gpsAlarm": "GPS Fault Alarm", "gpsNotInstalled": "GPS not installed", "gpsNotSynchronous": "GPS not synchronous", "gpsTime": "GPS Time", "groupCall_12L": "All level 1&2 devices", "groupCall_1L": "All level 1 devices", "groupCall_2L": "All level 2 devices", "inboundAlarm": "Inbound alarm", "infraredSensorAlarm": "Infrared sensor alarm", "inputcontent": "Input content", "inspector": "Patroller", "lowVol": "Voltage too low", "lowVoltageAlarm": "Low battery alarm", "mobileMonitoringStayAlarm": "Mobile monitoring stay alarm", "noLocationAlarm": "Emergency alarm (no location)", "offNetworkAlarm": "Talk around Alarm", "offline": "Offline", "online": "Online", "onlineType": "Online Type", "operation": "Operation", "outboundsAlarm": "Out bounds alarm", "overHeat": "Over heating", "overVol": "Voltage too high", "pass": "Qualified", "patrolTime": "Patrol time", "pllReceiveException": "PLL received exception", "pllTxException": "PLL sending exception", "pointNo": "Patrol point number", "powerOff": "Power off", "powerOn": "Enable", "processContext": "Process content", "processTime": "Process time", "processor": "Operator", "punchInWork": "Punching card while work time", "readTime": "Card reading time", "readUser": "Card user", "receiveName": "Recipient", "receiveTime": "Receiving time", "receiver": "Receiving repeater", "registered": "Registered", "removeSimulcastSystem": "Remove from simulcast system", "repeater": "Receiving repeater", "resetOn": "Power on by low battery reset", "selAlarmNote": "Query alarm logs", "selDetailNote": "Query patrol logs", "selGpsNote": "Query GPS track logs", "selOndutyNote": "Query shift records", "selSoundNote": "Query Call Logs Records History", "selSwitchNote": "Query device on and off logs.", "senderName": "Sender", "signalInterference": "Signal interference", "smsNo": "SMS No.", "soundFileName": "Recording file name", "soundSensorAlarm": "Sound sensor alarm", "sourceController": "Source controller", "speed": "Speed", "sponsor": "Di<PERSON>atcher", "standingWaveException": "Standing wave Exception", "startRoomOrgName": "Organization which started metting", "startRoomPerson": "Metting initiator", "startWork": "On duty", "statusException": "Exception", "statusReport": "Status Report", "syncCall": "Dynamic Group Call", "sysCenter": "Center", "targetChannel": "Target Channel", "times": "Time", "turnOffAlertMode": "Turn off alert mode", "turnOnAlertMode": "Turn on alert mode", "type": "Type", "userName": "Username", "userOrg": "User Unit", "walkingMobileMonitorPrompt": "Walking mobile monitor prompt", "weeks": "Week", "actions": "Action"}, "dialog": {"1month": "1 month", "1week": "1 week", "1year": "1 year", "3day": "3 days", "BaseStationPoint": "Base Station Patrol Point", "DMRDevice": "Mobile Radio", "Fri": "<PERSON><PERSON>", "Friday": "Friday", "Gsource": "GPS Point", "Hsource": "Active", "IKnow": "OK, I got it.", "Mon": "Mon", "Monday": "Monday", "Nsource": "Passive", "PowerOnPwdErrorThreshold": "Power-on password error threshold", "Quieting": "Quieting", "Sat": "Sat", "Saturday": "Saturday", "Sun": "Sun", "Sunday": "Sunday", "Thur": "<PERSON><PERSON>", "Thursday": "Thursday", "Tues": "<PERSON><PERSON>", "Tuesday": "Tuesday", "Wed": "Wed", "Wednesday": "Wednesday", "action": "Operation", "activeRFID": "Active RFID", "add": "Add", "addMapPoint": "Add Map Point", "addPreMadeSms": "add messages", "addStrangeNumberAuto": "Automatically add strangers to your address book", "addressBook": "Address book", "addressBookList": "Contacts list", "addressBookSettings": "Address book settings", "advancedDynamicencryption": "Advanced dynamic encryption", "agree": "Agree", "aheadTime": "Earliest Arrive", "alarmSet": "Alarm Control", "alarmSt": "Alarm Status", "alertTitle": "<PERSON><PERSON>", "allContacts": "All contacts", "allowCallOffline": "Allow calling offline targets", "allowCallOfflineInfo": "After this function is turned on, you can call the target who is not online.", "allowGeneralDMROnlineCall": "Allow regular DMR terminal access", "allowLoginManage": "Allow login scheduling management", "allowOffNetwork": "Allow off network", "allowOnlineCalls": "Allow online calls", "alreadyApplied": "Already applied", "alreadyAuth": "Already authorized", "always": "Always", "analog": "Analog", "analogAndDigital": "Analog and digital", "analogChannel": "Analog channel", "analogGatewayTerminal": "Analog gateway terminal", "analogHangTime": "Analog call suspension time ({unit})", "annex": "Annex", "answer": "Answer", "answeredCall": "Answered call", "area": "Area", "areaData": "Area data", "areaDown": "Area down", "areaName": "Area name", "areaNumber": "Area number", "areaSearch": "Area Search", "areaUp": "Area up", "authDevices": "Authorized terminal", "authorizationName": "License name", "autoCardReading": "Automatic card reading", "autoListeningTime": "Automatic listening time(s)", "autoPlaySms": "Auto play SMS", "autoPositioningTime": "Automatic positioning time(s)", "autoSignalDurtion": "Automatic send beacon duration ({unit})", "autoSignalInterval": "Automatic send beacon interval ({unit})", "automode": "auto mode", "availableColorCode": "Available color code", "back": "Back", "backlight": "Backlight", "bandpassFilter": "Bandpass filter", "baseGroupCallScheduling": "Base Group Call", "baseMobileRadio": "Base station mobile radio", "baseStation": "Base Station", "basicConfig": "Basic config", "batchSetting": "Batch copy", "batchSettingChannel": "Batch copy channel", "batteryVoltage": "Battery Voltage", "bdGateId": "Beidou Gateway ID", "bdsSwitch": "BDS switch", "beidouAddressBook": "Beidou Address Book", "beidouContact": "Beidou Contact", "beidouNav": "Beidou navigation", "beidouNumber": "Beidou number", "beidouPositionInfo": "Beidou positioning info", "beidouService": "Beidou Service", "beidouSetting": "<PERSON><PERSON><PERSON>", "beidouSms": "Beidou SMS", "beidouSwitch": "Beidou switch", "blackList": "Blacklist", "blackWhiteList": "Black and white list", "bootInterface": "Boot interface", "buttonDefinition": "Button definition", "callBackTarget": "Call Back target", "callPermissionIndication": "Call permission indication", "callRecord": "Call record", "callReminder": "Call reminder", "callTarget": "Call target", "callTransfer": "Call transfer", "callType": "Call type", "calledDevice": "Calling device", "calledTarget": "Called target", "callingContact": "Calling a contact", "canTalk": "Can talk", "cancel": "Cancel", "cancelCenterCHCtrl": "Cancel switching channel", "cancelEGChannel": "cancel Emergency Channel Dispatching", "cancelListen": "Disable Monitoring", "cancelPending": "Cancel pending", "cancelSetting": "Cancel setting", "centralCtrlLocate": "Positioning by Dispatch", "chConfigSwitch": "Channel config switch", "chDisplay": "Channel display", "chDisplayMode": "ChannelDisplayMode", "chDownSwitch": "Channel down switch", "chHangTime": "Channel hang time ({unit})", "chId": "Channel ID", "chName": "Channel name", "chType": "Channel type", "chUpSwitch": "Channel up switch", "channel": "Channel", "channelBandwidth": "Channel bandwidth", "channelConfigPassword": "Channel config password", "channelCtrl": "Dispatch Channel", "channelDown": "Channel down", "channelGroupCallSD": "Channel Group Call", "channelIdle": "Channel idle", "channelIdleIndication": "Channel idle indication", "channelList": "Channel list", "channelManager": "Channel manager", "channelSetting": "Channel setting", "channelShiftLevel": "Channel Shift Level", "channelUp": "Channel up", "checkCount": "Check Time(s)", "checkDate": "Check Date", "checkLineName": "Patrol Route name", "checkStartTime": "Start Time", "childRowIsEmpty": "No detailed information for the current line", "chipAutoResMechanism": "Chip automatic response mechanism", "chooseDate": "Select date", "cleanLogs": "clean logs", "clearAlarm": "Remove Alarm", "clearChannelConfig": "Clear channel config", "clearCheckedImg": "Clear", "clearOnceIn10Minutes": "Clear once in 10 minutes", "clickCheckImg": "Select image", "close": "Close", "closeMenuButton": "Close menu button", "cmdOpts": "Command", "colorCodes": "Color codes", "colorMapPoint": "The color point", "commandAgent": "Command agent", "commander": "Command Radio", "common": "Common", "configInfo": "Configuration", "configure": "Configure", "confirm": "Confirm", "confirmDelBtn": "Confirm Delete", "connect": "Connected", "contact": "Contact", "contactGrouping": "Contact grouping", "contactList": "Contact list", "controlTelephoneGateway": "TG810 Gateway", "controllerPointJumpTip": "The center of the map has jumped to the current equipment mark point, please click on the homepage to view", "copy": "Copy", "copyChannel": "Copy channel", "cpVersion": "CP version", "crossTOT": "Time Limit(mins)", "ctrlDMRID": "Device DMRID", "ctrlHistory": "Devices Event Log", "curChannelInfo": "Current channel info", "currentBattery": "Battery Level", "currentChannel": "Current Channel", "currentDevice": "Current terminal device", "customImg": "User-defined Image", "customPiggybackPowerLeve": "Custom piggyback power level", "customPower": "Custom power", "customVehiclePlv": "Customized vehicle power level", "customize": "customize", "data": "List", "dataChannel": "Data channel {num}", "dataTrsCmdRes": "Data transmission command response", "defCommunicationAddress": "Default communication address", "defWorkingTimeSlot": "Default working time slot", "default": "<PERSON><PERSON><PERSON>", "defaultChannel": "Default channel", "defaultFromArea": "Default from area", "defaultMainArea": "Default main area", "defaultUserArea": "Default user area", "delAllPrivilegeDevice": "Cancel all location permissions of the terminal", "delPrivilegeDevice": "Remove terminal location permission", "delPrivilegeDeviceFail": "Cancel terminal location permission failed", "delPrivilegeDeviceSuccess": "Cancel terminal location permission successfully", "delayTime": "Last Arrive", "delete": "Delete", "deleteContact": "Delete contact", "deletePrompt": "This operation will delete the data permanently,which may contains other important information,yes to continue?", "detailOfLineMaster": "Line details table", "details": "Details", "devAllStatus": "All Status", "devDisAuthCode": "Device disabled authentication code", "devEnAuthCode": "Device enabled authentication code", "devRemoteCtrl": "Device remote enable/disable", "deviceActive": "Device active", "deviceDMRID": "Terminal DMRID", "deviceDataTitle": "Terminal Management", "deviceDetect": "Device detect", "deviceDmrIdIsExisted": "The DMRID already exists", "deviceInfo": "Device Information", "deviceName": "Device Name", "devicePointJumpTip": "The center of the map has jumped to the current terminal mark point, please click on the homepage to view", "deviceRemoteDeath": "<PERSON>ce remote death", "deviceSettings": "Device settings", "deviceTime": "Device time", "deviceToWriteFrequency": "Device to write frequency", "deviceType": "Terminal type", "dialInList": "Dial-in list", "dialOutList": "Dial-out list", "digital": "Digital", "digitalAnalogChannel": "Digital analog channel", "digitalChannel": "Digital channel", "digitalGatewayTerminal": "Digital gateway terminal", "disListen": "Receiving Lockout", "disSL": "Receving & Transmitting Lockout", "disSend": "Transmitting Lockout", "disabledAllLed": "Disable all LED", "disconnect": "Disconnected", "dispatch": "Dispatching logs", "displayFrequency": "Display frequency", "displaysCallIdAndAlias": "Displays the call ID and alias", "dmrAddressBook": "DMR Address Book", "dmrContact": "DMR contact", "dmrIdNumber": "DMRID number", "dmrIdResult": "DMRID of Hexadecimal", "dmrService": "DMR service", "dmrSms": "DMR SMS", "dnsServerIp": "dnsServerIp", "download": "Download", "draftBox": "Draft box", "duplexSwitch": "Duplex switch", "dutyMachine": "Duty Radio", "dynamicGroupCallSD": "Dynamic Group Call", "dynamicOwnGroup": "Dynamic Own Group", "dynamicencryption": "Dynamic encryption", "early": "Early arrival", "edit": "Edit", "editContacts": "Edit contacts", "editContacts2": "Edit contacts", "editData": "Can edit data", "editScanList": "Edit scan list", "editUserPerm": "Edit user permissions", "editZone": "Edit area", "effectEndTime": "End Date", "effectStartTime": "Start Date", "effectType": "Effect Type", "effectiveDate": "Valid Date", "electricFence": "GPS Fence", "electricalAdjustmentFilter": "Electrical adjustment filter", "emerHangTime": "Emergency call suspension time ({unit})", "emergency": "Emergency", "emergencyAlarm": "Emergency Alarm", "emergencyDispatch": "Emergency Call", "emission": "Emission", "emissionNumber": "@:dialog.emission {num} (MHz)", "empty": "Empty", "enable": "Enable", "enableAutoMonitoring": "Enable automatic monitoring", "enableEGChannel": "Activate Emergency Channel Dispatching", "enableEmergencyAlarm": "Emergency Alarm", "enableInAndOut": "Activate Fence Monitor,Not monitor after Cross Fence", "enableInCacnelOut": "Activate Entering into Fence Monitor,cancel Going out Fence Monitor", "enableListen": "Enable Monitoring", "enableOutCacnelIn": "Activate Going out Fence Monitor,cancel Entering into Fence Monitor", "enablePopUpBlacklist": "Enable dial-out blacklist", "enablePopUpWhitelist": "Enable dial-out whitelist", "enableStackingBlacklist": "Enable dial-in blacklist", "enableStackingWhitelist": "Enable dial-in whitelist", "endTime": "End Date", "errorTestFire": "Error test - Fire", "errorTestReceive": "Error test - Receive", "exclusiveUser": "Specified User", "exit": "Exit", "export": "Save Date to", "exportConfig": "Export config", "findUsbDevice": "Find USB device", "firmwareVersion": "Firmware version", "firstLevelArea": "First level area", "fixedLineNumber": "Fixed-line number", "fixedLineNumberHelp": "0xx-xxxxxxx-xxxx", "flag": "<PERSON>", "forciblyOutNetworkAlarm": "Forcibly Talk Around", "freqAndChDisplay": "Frequency + Channel display", "freqDisplay": "Frequency display", "frequencyRange": "Frequency Range(MHz)", "frequentContacts": "Frequent contacts", "fullCall": "Full call", "fullCallPerm": "All call permissions", "gatewayId": "Gateway ID", "gatewayIp": "gatewayIp", "gatewayName": "Gateway name", "gatewayPermission": "Telephone terminal License", "gatewayRule": "Gateway rule", "ge": "Piece", "generalDmr": "Conventional DMR terminal", "generalSetting": "General settings", "getLngLat": "Capture from Map", "gpsAlarm": "GPS Fault", "gpsCardNo": "GPS Card number", "gpsRadius": "GPS Radius (m)", "gpsVirtualSet": "GPS Patrol Point", "grantUser": "Authorized User", "groupCall": "Group call", "groupCallDialing": "Group call dialing", "groupCallMapping": "Group call mapping", "groupHangTime": "Group call hang time ({unit})", "groupName": "Group name", "handmode": "hand mode", "hangTime": "Hang time(ms)", "hangup": "Hangup", "hasBeenDisListen": "Receiving Forbidden", "hasBeenDisSend": "Transmitting Forbidden", "hasBeenRoaming": "Roaming", "hasEGAlarmAndLocate": "Alarm and Positioning", "hasFixedGroupSD": "Fixed Group Call", "hasGpsAutoCtrlLocate": "GPS Positioning", "hasLevelGroupCallSD": "Level Group Call", "hasNetworkRoamingSD": "Roaming Dispatch", "hasOffNetwork": "Talked Around", "haveruntime": "Single time (m)", "high": "High", "highAndLowPowerSwitch": "High and low power switch", "highFrequency": "High frequency (MHz)", "idShownMinLen": "ID minimum display digits", "imageMapPoint": "The flag point", "imbeSn": "Vocoder SN", "importConfig": "Import config", "importExcel": "Import excel", "inCtrlAlarm": "Enter Boundary", "inbox": "Inbox", "incomingCall": "Incoming call", "index": "No.", "initTransferTargetTree": "Please select transfer target", "insLine": "Patrol Route", "insPoints": "Patrol Point", "insRules": "Patrol Statistics", "installLocation": "Install location", "interPhoneWfPerm": "Intercom writing frequency", "interfacePos": "Interface location", "internetGateway": "Internet gateway", "interphone": "Intercom", "ipSettingMode": "ipSettingMode", "isGroupCall": "Is group call", "join": "Join", "joinBlackList": "Join the blacklist", "joinWhiteList": "Join the whitelist", "keepAdding": "Keep adding", "keepSending": "Keep sending", "key01": "Key 01 ({type})", "key02": "Key 02 ({type})", "key03": "Key 03 ({type})", "key04": "Key 04 ({type})", "keyboardLock": "Keyboard lock", "km_h": "Km/h", "languageSettings": "language settings", "languageType": "Language type", "lastCheckDevice": "Final Patrol Device", "lastCheckTime": "Final Patrol time", "lastCheckUser": "Final Patroller", "lastDataTime": "Last data time", "lat": "Latitude", "latDif": "Lat Difference", "late": "Late", "launchContact": "Launch contact", "leaveTOT": "Time Limit(mins)", "ledIndication": "LED indication", "ledIndicator": "LED indicator", "level": "Level", "lineName": "Route Name", "linePointTitle": "Patrol Point", "linePointType": "Type", "lineTitle": "Patrol Route", "listenChannel": "Monitor Channel", "listenGroup": "Have to listen", "listenTime": "Monitor Time(secs)", "lngLat": "Lng & Lat", "local2net": "Local calls treated network calls", "localCall": "Local call", "localEditRxGroup": "Allow local editing of receiving groups", "localName": "Local name", "localNumber": "Local number", "localPort": "localPort", "locateCount": "Reporting GPS Time(s)", "locateCtrl": "GPS Control", "locateSpacing": "Positioning Interval(secs)", "locateTime": "Positioning time (Sec)", "lockedDev": "Lockout", "lockedStatus": "Status", "lon": "Longitude", "lonDif": "Lon Di<PERSON>", "longPress": "Long press", "longPressDuration": "Long press duration(ms)", "lookGpsPointData": "View GPS data", "low": "Low", "lowFrequency": "Low frequency (MHz)", "mainZone": "Primary area {id}", "manualDialing": "Manual dialing", "mapDisplayName": "Name on Map", "mapMarker": "Map Mark", "mapMarkerH": "height", "mapMarkerW": "width", "mapPointJumpTip": "The center of the map has jumped to the current marked point, please click on the homepage to view", "mapPointType": "Patrol point type", "mappingTarget": "Corresponding target", "markerSize": "<PERSON><PERSON>", "maxLat": "<PERSON>", "maxLon": "<PERSON>", "maxSpeakTime": "Call time limit (s)", "memberList": "Member list", "menuHangTime": "Menu hang time(s)", "menuSettings": "Menu settings", "meshDevice": "MC-N terminal", "meshGateway": "MC-N gateway", "meshGatewayDevice": "Mesh gateway terminal", "mi_h": "Mi/h", "mid": "Mid", "minLat": "<PERSON>", "minLon": "<PERSON>", "minute": "Minutes", "miss": "Missed checking", "missedCall": "Missed call", "mobile": "Mobile", "mobileCtrl": "Movement Control", "mobileCtrlAlarm": "Motion Alarm", "mobileDevReqDevGps": "Network intercom terminal requests terminal location information", "mobileDevice": "Mobile terminal", "mobilePhoneNumber": "Mobile phone number", "mobilePhoneNumberHelp": "1xxxxxxxxxx", "mobileTerminalType": "Terminal Type: {type}", "mode": "Mode", "model": "Model", "monitorSwitch": "Monitor on/off", "moreLinePointHty": "More Patrol Log", "moveDn": "DOWN", "moveUp": "UP", "msgcontentplaceholder": "please input your message content", "msglimit": "Message count should not more than 10", "muteAll": "Mute all", "muteTone": "Mute tone", "name": "Name", "neglect": "Neglect", "networkIp": "networkIp", "networkMask": "networkMask", "networkService": "Network service", "networkSetting": "Network settings", "networkSpeaking": "Network speaking", "networkSwitch": "Network switch", "networkTimeSlot": "Networking channel", "new": "New", "newContact": "New contact", "newSmsMessage": "New sms message", "nmi_h": "Nmi/h", "no": "No", "noFunction": "No function", "noMatchText": "No matching data", "nonCompacting": "Non-compacting", "nonStandard": "Non standard", "none": "None", "noneLevel": "No level", "notFoundGpsData": "Not Found GPS Data", "notSendSmsData": "Unacknowledged SMS", "notes": "Notes", "nothing": "Nothing", "numKey": "Number key ({num})", "number": "No.", "occurUnderVoltageAlarm": "Low Power", "off": "Off", "offNetwork": "Off network", "offNetworkGroupCallHangTime": "Off-network group call hang time(ms)", "offNetworkSingleCallHangTime": "Off-network single call hang time(ms)", "offlineRepeater": "Offline repeater", "offlineSign": "Off-network sign", "on": "On", "onOff": "Switch", "oneLevel": "Level 1", "onlineRepeater": "Online repeater", "openCenterCHCtrl": "Turn on switching channel", "openListenFn": "Monitor", "openMobileCtrl": "Motion Monitor", "openOutCtrl": "Enter Boundary Monitor", "openPeripheralFn": "Peripheral Functions", "openSchedulingFn": "Dispatch", "openSentryCtrl": "Sentry Monitor", "opentInCtrl": "Overstep Boundary Monitor", "orgDMRID": "Group Call DMRID", "orgFullName": "Full Name", "orgNoMapPointMsg": "There is currently no map marker for the current unit, please add it first", "orgPointJumpTip": "The center of the map has jumped to the current unit mark point, please click on the home page to view", "orgShortName": "Short Name", "orgTitle": "Organizations", "orgsAddMapPointMsg": "If checked and successfully added, it will jump to the map marker page to add marker points.", "outCtrlAlarm": "Overstep Boundary", "outbox": "Outbox", "outgoingCall": "Outgoing call", "parentController": "Parent controller", "parentOrg": "Organization", "parentOrgName": "Superior", "parentZone": "Parent area", "passThroughMode": "Pass through mode", "patrolSystem": "Patrol system", "penNo": "Fence No.", "permissionConditions": "Permission conditions", "phoneBlackWhiteList": "Phone black and white list", "phoneBook": "Phone book", "phoneNoInputHelp": "Phone number Input Instructions", "picSize": "Size of picture is 120*40", "picture": "Photo", "playTrack": "Play the Track", "plosive": "Plosive", "pocDevice": "POC terminal", "pocDeviceManage": "POC terminal configuration", "pocFormErrorTip": "Please fill in the complete POC terminal information", "pocTreeTitle": "Please select address book", "pointCount": "Patrol Times", "pointIndex": "Point Number", "pointName": "Patrol Point Name", "pointRfid": "Patrol Point RFID", "pointSerialNo": "Point Serial", "pollingTimeSlice": "Polling time slice(s)", "popUpBlacklist": "Dial-out blacklist", "popUpWhitelist": "Dial-out whitelist", "postFence": "Sentry Post", "postName": "Job Title", "postTitle": "Job Title", "power": "Power", "powerLevel": "Power level", "powerOnPwd": "Power-on password", "powerSavingMode": "Power saving mode", "powerSwitch": "Power switch", "praviteHangTime": "Single call hang time({unit})", "preMadeSms": "Pre-made SMS", "predefinedPhoneBook": "Predefined phone book", "primaryAreaID": "Primary area ID", "priority": "Priority", "privelege": "Privilege", "privilegeDevice": "Terminal location authorization", "prochatDevice": "Prochat terminal", "prochatDeviceAssociatedName": "Associated prochat terminal", "prochatDeviceList": "Prochat terminal list", "prochatDomain": "ProChat domain name or IP", "prochatGateway": "Prochat gateway", "prochatGatewayConfig": "ProChat fusion gateway config", "prochatGatewayDevice": "Prochat gateway terminal", "prochatNo": "ProChat fusion gateway account", "prochatPassword": "password", "prochatPort": "Prochat port", "prochatUserNo": "User account", "programmingPwd": "Programming password", "query": "Query", "queryAllChannel": "Query all channels", "queryNotSentSMS": "Query not confirm SMS", "queryRepeaterInfo": "Query repeater information", "querySentSMS": "Query SMS history", "querySetting": "Query settings", "quickDailEnable": "Quick dial on the main interface", "readData": "Read", "readFromDevice": "Copy channel config from device", "readRfidFormDev": "select from device upload", "realOrg": "Real organization", "realplayer": "DownLoad Player", "receive": "Receive", "receiveDevice": "Received device", "receiveFrequency": "Receiving frequency", "receiveGroup": "Received group", "receiveGroupList": "Receiving group list", "receiveLowPowerPromptInterval": "Receive low power prompt interval(s)", "receiveNumber": "@:dialog.receive {num} (MHz)", "receiveOnly": "Receive only", "receivingAnalogSubtoneCode": "Receiving analog subtone code", "receivingDigitalSubtoneCode": "Receiving digital subtone code", "receivingList": "Receiving list", "receivingSubtoneType": "Receiving subtone type", "recordCompRatio": "Recording compression ratio", "recordEnable": "Recording enable", "recording": "Recording", "records": "Records", "refuse": "Refuse", "registCode": "Registration code", "registerToSystem": "Register to system", "rejectingStrangeCalls": "Rejecting strange calls", "remoteCtrlPwd": "Remote control interface password", "remoteDeviceDisabled": "Remote device disabled", "remoteDeviceEnable": "Remote device enable", "remoteMonitor": "Remote monitoring", "removeFenceAllCtrl": "Cancel Fence Monitor", "repeater": "<PERSON><PERSON><PERSON>", "repeaterId": "Repeater ID", "repeaterInfo": "Repeater info", "repeaterName": "Repeater name", "repeaterNumber": "Repeater number", "repeaterPluginHangTime": "Repeater plug-in hang time", "repeaterTimeSlot": "Repeater time slot", "repeaterVirtualTerminal": "Repeater virtual terminal", "repeaterWfPerm": "Repeater write frequency", "repowerOnAndRestart": "Re-power on and restart", "reqDevice": "Apply for terminal", "requiredRule": "Required <PERSON>em", "reset": "Reset", "responseTimeout": "Response timeout(s)", "responseTimeout1": "Response timeout(ms)", "restartRepeater": "Restart repeater", "restoreDefaultIPAddress": "Restore default IP address", "reverseSubtoneDigital": "Reverse subtone digital", "rgpsMode": "Reliable positioning transmission", "roamInterface": "Roaming interface", "rootDirList": "Root directory list", "rpgsModeInfo": "After this function is turned on, if the terminal uploads the location but fails to get confirmation from the system, it will be automatically saved locally on the terminal and automatically uploaded after the terminal reconnects with the system.", "rssiThreshold": "RSSI threshold (dBm)", "ruleName": "Rule Name", "ruleTarget": "Rule target", "ruleTitle": "Patrol Rules", "runNotesTitle": "Running Log", "rxFrequency": "Receiving frequency (MHz)", "rxGroup": "Receiving group", "rxGroupsSetting": "Receive Group Settings", "salerId": "agency ID", "satellitePosition": "Satellite positioning", "satellitePositionService": "Satellite positioning service", "satellitePositionSetting": "Satellite positioning setting", "save": "Save", "savePenNo": "<PERSON><PERSON> saved number", "scanEnable": "Scanning switch", "schedulingSt": "Dispatching Status", "secondaryArea": "Secondary area", "secondaryAreaID": "Secondary area ID", "selComputer": "Select from local", "selImgPrompt": "Only Allow .jpg/.png file,not exceeds 100kb", "select": "Select", "selectCallTarget": "Please select call target", "selectColor": "Select Color", "selectImage": "Select Image", "selectListenGroup": "Please select listening group", "selectPoint": "Select Point", "selectZone": "Please select a channel area", "selectZoneCh": "Specify boot area / channel", "sendCmdHistory": "Sending command history", "sendCmdTitle": "Dispatch", "sendCommand": "Can send command", "sendCount": "The number of transmissions", "sendGroup": "De<PERSON>ult send group", "sendMessages": "send messages", "sendPreambleDuration": "Transmitting preamble duration(ms)", "sendTime": "Sending Time", "sendTimeLimiter": "Send time limiter(s)", "sendedSmsData": "SMS history", "senderDevice": "Send device", "sentinelRadius": "Radius Range", "sentryCtrlAlarm": "Sentry Alarm", "serialNo": "Serial No.", "serialNumber": "Serial number", "serverAddress": "serverAddress", "serverPort": "serverPort", "serverSetting": "Server settings", "setReceivingGroup": "Configure fixed subscriptions", "settingSt": "Setting Status", "setupPhoneInfo": "Gateway terminal settings", "shortNumber": "Short no.", "shortNumberMapping": "Telephony gateway short number mapping", "shortPress": "Short press", "shortestDistance": "shortest distance(m)", "showDeviceNameDisplay": "Standby display device name", "showLevel": "Display level", "silent": "Silent", "simulation": "Simulation", "simulcastController": "Simulcast controller", "simulcastRepeater": "Simulcast repeater", "simulcastSystem": "Simulcast system", "singleCall": "Single call", "singleCallConfirm": "Single call confirm", "singleCallDialing": "Single call dialing", "singleCallMapping": "Single call mapping", "sipDomain": "SIP domain name or IP", "sipGateway": "SIP gateway", "sipGatewayConfig": "SIP gateway config", "sipGatewayDevice": "SIP gateway terminal", "sipNo": "SIP account", "sipPassword": "SIP password", "sipPort": "SIP port", "sipProtocolDevice": "SIP protocol terminal", "sipProtocolDevicePassword": "SIP protocol terminal password", "sipProxyGateway": "Whether to run the SIP gateway in the background", "sipServerGateway": "SIP server gateway", "sipServerGatewayConfig": "SIP server gateway config", "sipServerGatewayDomain": "Public SIP server domain name or IP", "sipServerGatewayListenPort": "SIP server gateway port", "sipServerGatewayRTPPortRange": "SIP server gateway RTP port  range", "sipSoundLocale": "SIP voice prompt language", "size": "Shortest TX distances(m)", "slotMode": "Slot mode", "sms": "SMS", "smsContent": "SMS Content", "smsNotification": "SMS notification", "smsType": "SMS type", "sortValue": "Sort", "soundTip": "Beep on/off", "sourceDevice": "The source terminal", "speakDevice": "Speak device", "speakPrompt": "Hold down F2/Space speaking", "speedUnit": "Speed unit", "squelchCalibration": "Squelch calibration", "squelchLevel": "Squelch level", "squelchSwitch": "Squelch switch", "stackingBlacklist": "Dial-in blacklist", "stackingWhitelist": "Dial-in whitelist", "standard": "Standard", "standardEnableAndMute": "Standard enable, standard mute", "standbyInterface": "Standby interface", "startCopy": "Start copying", "startSetting": "Start setting", "startTime": "Start Date", "starting": "Start", "staticEncryption": "Static encryption", "status": "Status", "stopAlarmSound": "Stop Alarm sound", "stopTOT": "Time Limit(mins)", "subAudio": "Sub-audio", "subZone": "Secondary area {id}", "subaudioSetting": "Subaudio setting", "subtoneDigital": "Subsonic digital", "suspendTime": "Suspend time", "switchCh": "Switch Channel", "switchChannel": "Switch channel", "switchTransmitPower": "Switch transmit power", "systemCenter": "System center", "tagName": "Tag Name", "tailCancellation": "Tail cancellation", "tailSelection": "Tail selection", "targetDevice": "Target terminal", "telecontrol": "Remote Control", "telephoneDevice": "Telephone device", "telephoneGateway": "Gateway terminal", "telephoneNo": "telephone number", "terminalAlias": "Terminal alias", "terminalLevel": "Terminal level", "terminalName": "Terminal name", "tertiaryAreaID": "Tertiary area ID", "testMachineSignalDuration": "Test machine signal duration ({unit})", "testMachineSignalInterval": "Test machine signal interval ({unit})", "textImportant": "Important SMS", "textInfo": "General SMS", "textMsg": "Text Message", "threeLevel": "Level 3", "timeSegment": "Valid Period", "timeSetting": "Time setting", "timeSlot": "Time slot {num}", "timeSlots": "Time slot", "timeZone": "Time zone", "timeZoneHours": "Time zone hours", "timeZoneMinutes": "Time zone minutes", "timeZoneSet": "Time zone setting", "toneOrTip": "Tone/tip", "totPwdUpdateDelay": "TOT password update delay(s)", "tr925HighPower": "High power -- 25W(car) / 15W(backpack)", "tr925LowPower": "Low power -- 5W(car) / 3W(backpack)", "tr925MediumPower": "Medium power -- 10W(car) / 6W(backpack)", "trailCtrl": "Trace Control", "trailSpacing": "Trace Intervals (secs)", "transFrequency": "Transmitting frequency", "transTimeLimit": "Transmission time limit", "transmitAnalogSubtoneCodes": "Transmit analog subtone codes", "transmitDigitalSubtoneCodes": "Transmit digital subtone codes", "transmitSubtoneDigital": "Transmitting subtone type", "triggerCardReading": "Button trigger card reading", "turnOn": "Turn on", "twoLevel": "Level 2", "txFrequency": "Transmitting frequency (MHz)", "txPower": "Transmit power", "type": "Type", "uDiskMode": "U disk model", "uDiskModePassword": "U-Disk mode password", "unListenGroup": "Not to listen", "unencrypted": "Unencrypted", "update": "Update", "updateMapPoint": "Update Map Point", "useDevice": "Use device", "useUnit": "Use unit", "userLoginName": "Login Name", "userLoginPass": "Login Password", "userName": "User Name", "userPhone": "Mobile Phone", "userRfid": "User RFID", "userZone": "Tertiary area {id}", "usersDataTitle": "User Management", "usersPrivelegeSet": "Privilege Setting", "vcController": "Virtual cluster controller", "vcRepeater": "Virtual cluster repeater", "version": "version", "versusEnableAndMute": "Versus enable, standard mute", "versusEnableOrMute": "Versus enable, or mute", "viewContacts": "View contacts", "vioAuto": "Automatic(Not Play Sound)", "vioPlay": "Play sound", "virOrg": "Virtual organization", "virtualCluster": "Virtual cluster", "virtualClusterDevice": "Virtual cluster intercom", "virtualTimeSlot": "Virtual cluster transmission time slot", "vocalRule": "Vocal rule", "voiceControl": "Voice control", "voiceCryptoKey": "Voice encryption key", "voiceCryptoType": "Voice encryption type", "voiceCtrl": "Monitor Control", "voiceDelay": "Voice delay(ms)", "voiceFrameRelayCheck": "Voice frame relay check", "voiceIndication": "Voice indication", "voiceLevel": "Voice level(VOX)", "voicePrompt": "Voice Prompt", "volumeDown": "Volume reduction", "volumeUp": "Volume plus", "whiteList": "Whitelist", "workingSt": "Working Status", "workspaceAcrossBorders": "Overstep Boundary", "writeIn": "Write in", "yes": "Yes"}, "dtable": {"all": "All", "buttons": {"colvis": "Show/hide column"}, "emptyTable": "No data available in table", "info": "Showing _TOTAL_ entries", "infoEmpty": "Showing 0 to 0 of 0 entries", "infoFiltered": "(Filtered from _MAX_ total entries)", "lengthMenu": "Show _MENU_ entries", "loadingRecords": "Loading...", "paginate": {"first": "<span class='bf-icon gw-first'></span>", "last": "<span class='bf-icon gw-last'></span>", "next": "<span class='el-icon-caret-right'></span>", "previous": "<span class='el-icon-caret-left'></span>"}, "processing": "Processing...", "search": "<span class='el-icon-search'></span>", "zeroRecords": "No matching records found"}, "dynamicGroup": {"addMember": "Add member", "alreadyJoined": "{member} has joined {group}", "answeredExit": "Answered exit", "cannotModifyQuickTempGroup": "Cannot modify the members of the quick temporary group", "deleteDynamicGroupTips": "Whether to delete the dynamic group？", "device": "<PERSON><PERSON>", "dynamicGroupMembers": "Dynamic group members", "dynamicGroupPermission": "Dynamic group permission", "exitWithoutAnswer": "Exit without answer", "expired": "Expired", "expiredDynamicGroup": "Temporary group or task group has expired", "fast": "Fast", "fastDynamicGroupNoModify": "Fast temp group, no modify allowed", "groupCall": "Group call", "groupCallDevice": "Group call device", "groupMemberUpperLimit": "Group members has reached the upper limit", "groupMembersLimit": "Group members Over limit", "groupName": "Group name", "invalidTaskGroup": "Task group has expired", "isForceDelete": "Is force delete?", "joinWithoutAnswer": "Join without answer", "jumpToNetworkCall": "Whether to jump to network call?", "member": "Member", "memberDetails": "Member details", "memberName": "Member name", "memberOrg": "Member org.", "noDynamicGroupDmrId": "No dynamic group DMRID available", "noGroupMembers": "No group members selected", "noSuchDynamicGroup": "The dynamic group was not found", "noUpdates": "No membership changes, no updates required", "normal": "Normal", "notFoundDbHandler": "Unable to find the corresponding database, please log in again", "notFoundDynamicGroup": "The dynamic group was not found, please try again", "notFoundMemberInDynamicGroup": "The member could not be found in the dynamic group", "pcDeviceExitTaskGroup": "Command agent {pc<PERSON><PERSON><PERSON>} has exit {dynamicGroup} task group", "pcDeviceJoinedTaskGroup": "Command agent {pc<PERSON><PERSON><PERSON>} has joined {dynamicGroup} task group", "permDied": "Permission Died", "preempted": "Preempted", "removeMember": "Remove member", "repeatGroupName": "Duplicate dynamic group name", "taskGroup": "Task group", "taskGroupExpiredWithUpdate": "The task group has expired, the operation failed!", "tempGroup": "Temp group", "tempGroupInvalidAndDelete": "Automatically delete after invalidation?", "title": "Dynamic group", "updateFailedDetail": "Update failed member details", "updateGroupMembers": "Update dynamic group (%(group)) members"}, "error404": {"goBack": "Go Back", "goHome": "Go Home", "notFoundPage": "The visited page does not exist..."}, "header": {"CN": "中文", "EGAlarmOnMapCenterTips": "Emergency Alarm on Map Center", "EN": "English", "FR": "Français", "InsOnMapTips": "Patrol Tips on Map", "InspectionNotice": "Patrol Notice", "Korean": "Korean", "Russian": "Russian", "accountManager": "Account management", "callingTips": "Calling Tips", "checkChannelListenGroup": "Check terminal receiving group configuration", "clearCache": "reload", "detectNewUiTips": "A new page has been detected, do you want to switch?", "emergencyAlarmTips": "Emergency Alarm Notice", "fancytreeSortType": "List tree sort: the terminal is in front of the unit", "fullScreen": "Switch to full screen", "language": "Language", "logo": "Logo Image", "logoName": "BelFone SDP Radio System", "moveText": "Moving Title", "moveTitle": "BelFone SDP Radio System", "navBtn": "Nav", "newDevOrCtrlData": "Display unregistered device info", "newPwd": "New password", "newPwd2": "Confirm password", "oldPwd": "Old password", "onAndOffTips": "Power on/off Notice", "onMapDisplayLinePointName": "Patrol Check Point Name", "openEGAlarmSound": "Emergency alarm play sound", "orgList": "OrgList", "scrollMoveTitle": "Does the title scroll", "serverTitle": "Server Connected", "setting": "Setting", "showActivePointBattery": "Active Point Low Power Notice", "showCrossBorderAlarm": "Show cross border alarm info", "showCtrlMarker": "Device tag on the map", "showCtrollerStats": "Prompt when the device is disconnected", "showDispatchInfo": "Prompt scheduling information", "showIotDeviceMarkers": "Display IoT terminal markers on the map", "showOnlyLocateTerminals": "Only valid location markers are shown on the map", "showOnlyOnlineTerminals": "Only online terminal markers are shown on the map", "showSmsResNotify": "Show terminal received SMS notification", "siteTitle": "BelFone SDP Dispatching System", "startAndEndWorkTips": "Start/End Work Notice", "switchNewUi": "Switch to new page", "switchOldUi": "Switch old version of the page", "switchOldUiTips": "Do you want to switch to the old version of the page?", "switchnewUiTips": "Do you want to switch to the new version of the page?", "sysSet": "System Setting", "userName": "User Name", "userSet": "Personal Setting"}, "iot": {"acOff": "AC off", "acOn": "AC on", "devId": "Terminal ID", "duplicateID": "Duplicate ID", "energySavingLamps": "Energy-saving lamps", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "iotInspectDevice": "IoT inspection terminal", "lastCmd": "Last command", "normalClockIn": "Normal clock in", "orgIdError": "Owned org data error", "receiveUnknownIotDevice": "Received unregistered IoT terminal data", "smokeDetector": "Smoke detector", "tempDetector": "Temperature / Humidity detector", "tempReport": "Report temperature and humidity", "temperature": "Temperature", "terminal": "IOT terminal", "thingsCard": "Things card"}, "iotDevHistory": {"antiFoldAlarm": "Anti-demolition alarm", "cmdHex": "Cmd", "cmdTime": "Cmd Time", "emergency": "Emergency Alarm", "heartbeat": "Heartbeat", "lowPower": "Low Power Alarm", "overTemperatureAlarm": "Over temperature alarm", "recvTime": "Recv Time", "remove": "Remove Alarm", "report": "Report"}, "loginDlg": {"account": "System Login", "adminPlatform": "DataManagement platform client", "backLogin": "Back to Login", "connected": "Server Connected", "connecting": "Server Connecting", "disconnect": "Server Connecting Fail", "dispatchPlatform": "Scheduling platform client", "loading": "Loading...", "logging": "Logging in", "login": "<PERSON><PERSON>", "loginPassword": "Enter account password", "loginSuccess": "<PERSON><PERSON> successfully", "loginTimeout": "Login Timeout,Please Login Again", "loginWind": "Log in", "name": "Enter account name", "noAdminAndDispatch": "No administrator or dispatcher privileges", "noHasUser": "The user don't exist,Please Login Again", "oldSession": "Your session has expired,Please Login", "password": "Password", "passwordError": "Wrong Password", "platform": "Platform", "remember": "Remember", "system": "SYSTEM ID", "upgradeServerTip": "The current server version is too low ({currentServerVersion}), please upgrade to ({latestServerVersion}) version!"}, "map": {"DIST": "<PERSON>ng<PERSON>", "E": "East", "EN": "East-north", "ES": "East-south", "EbyN": "East by North", "EbyS": "East by South", "N": "North", "NbyE": "North by East", "NbyW": "North by west", "S": "South", "SbyE": "South by East", "SbyW": "South by West", "W": "West", "WN": "North-west", "WS": "West-south", "WbyN": "West by North", "WbyS": "West by South", "clickMapGetCoordinates": "Please click on the map to get coordinates", "degrees": "degree", "distances": "distances", "fastForward": "Fast Forward", "mapLevel": "Zoom", "pased": "Pause", "play": "Play", "quitMapMsg": "Track playback has exited", "resetBearingToNorth": "Reset bearing to north", "satellite": "satellite", "selectCoordinates": "Select coordinates", "selectCoordinatesTips": "Please activate the coordinate selection control {iconEl} first, then press the mouse and drag to select the range, and click the selected range to confirm the result.", "slowDown": "Slow Down", "stop": "Quit", "streets": "streets", "zoomIn": "Zoom in", "zoomOut": "Zoom out"}, "msgbox": {"FailedReadSome": "File {fileName} failed to read", "ScheduleStartTime": "Dispatching start time", "ScheduleTargetChannel": "Dispatching target channel", "UsbServerConnectError": "USB terminal server connection error", "UsbServerConnectSuccess": "USB terminal server connection success", "UsbServerNotConnected": "USB terminal server is not connected", "addError": "Added failed", "addSuccess": "Added successfully", "addToSql": "Open window and add a new device", "alarmSetSendOk": "Send alarm setting command successfully", "allChannel": "All channels", "alreadyFirst": "Already in the first row", "alreadyLast": "Already in the last row", "areInspections": "Patrolling", "areaResult": "Result of area searching", "arrivalTimes": "Set arrival time", "associatedProchatDeviceNotSet": "Associated Prochat terminal is not set", "batchSettingChannelFailed": "Batch copy channel failed. Please try again later", "batchSettingChannelSuccess": "Batch copy channel successful", "bdNumberUnique": "The number cannot be repeated", "bdUserNameUnique": "Contact name cannot be repeated", "being": "Being", "canNotDeleteProchatGatewayDevice": "Cannot delete prochat gateway device", "canNotDeleteProchatUser": "Cannot delete prochat user", "canNotDeleteRootUserData": "Cannot delete root user data", "canNotDeliveredByGroup": "The command cannot be delivered by group", "canNotEditProchatGatewayDeviceDmrId": "Cannot edit Prochat gateway device's dmrId", "canNotEditProchatGatewayDeviceType": "Cannot edit Prochat gateway device's type", "canNotEditProchatGatewayType": "Cannot edit Prochat gateway's type", "canNotEditProchatUserSeIfId": "The serial number of Prochat gateway user cannot be edited", "canNotEditRootUserData": "Cannot edit root user data", "canNotEditSipServerGatewayType": "The SIP Server gateway type cannot be edited", "cancelAlarmCmd": "cancel alarm setting command", "cancelEGChannelCmd": "Cancel emergency dispatch", "cancelGPSVirCmd": "Cancel setting GPS point command", "cancelGpsInsSet": "Canceled GPS patrol point", "cancelMoveCtrlCmd": "cancel movement monitor command", "cancelSchedule": "Cancel scheduling", "cancelSentinelCmd": "cancel Sentry Post command", "cancelTrailCmd": "cancel tracking command", "cancelVioCmd": "cancel voice monitoring command", "cannotInputChinese": "Can't input Chinese", "cannotSendCb08Cmd": "USB Connector is not connected, no voice monitoring", "cannotSetAsSubOrg": "The parent unit cannot be set as its own subordinate unit", "cardNo": "Card Number", "centerChannelCtrl": "Send switching channel successfully", "channel": "Channel", "channelBusy": "Channel busy", "channelCtrlSendOk": "Send channel dispatch command successfully", "channelNotSetListenGroup": "{channel} channel is not configured with a receiving group, and calls cannot be scheduled normally", "character": "Character", "checkADevice": "Please select a device to import original GPS data", "clearAlarmOk": "Cancel alarm successfully", "clearEGAlarm": "cancel emergency alarm", "clearSendListenSt": "cancel lockout", "configSuccess": "Configuration is successful", "confirmAgainToDel": "Please confirm again if you want to delete", "confirmGpsInsSet": "Activate GPS patrol point", "controllerGatewayUniqueDevice": "The phone gateway device can only associate one relay", "controllerGatewayUniquePhonePos": "The next trunk interface of the same relay can only be associated with one telephone gateway.", "ctrlData": "Device data", "ctrlDisConnect": "Device is disconnected", "ctrollerFailureAlarm": "<PERSON><PERSON>", "currentCanInput": "Here can be input", "currentRfidTime": "This card reading time", "currnetLocate": "Current position", "dc01NotFoundLinePoint": "No patrol point data found, patrol point card number: {rfid}", "dc01UserDataNotFound": "User data not found, RFID card number：{rfid}", "delError": "Deleted failed", "delLinePointError": "The patrol point is used by patrol route, Please delete patrol route firstly", "delOutsidePermissions": "Cannot delete data outside of permissions", "delSuccess": "Deleted successfully", "deleteChannelFailed": "Delete failed, the current channel cannot be deleted", "deviceData": "Device data", "deviceFault": "<PERSON><PERSON>", "deviceModelError": "Device model error", "deviceNotReturnData": "Terminal does not return data", "deviceTerminateReadOpt": "Terminal terminates the reading operation", "disDelOrg": "This patrol point is used by other route, Please delete the patrol route.", "disconnectDevice": "Can't connect device", "downloadFailed": "Download failed", "downloading": "downloading", "dui": "Yes", "duplicateName": "Duplicate name, please re-enter", "emptyDMRID": "DMRID can't be null.", "emptyText": "No data", "enableAlarmCmd": "Activate alarm setting command", "enableEGChannelCmd": "Activate emergency dispatch", "enableGPSVirCmd": "Set GPS point command", "enableInAndOut": "Activate command for boundary in and out", "enableInCtrlCmd": "Activate command for entering into boundary", "enableMoveCtrlCmd": "Activate movement monitor command", "enableOutCtrlCmd": "Activate command for going out boundary", "enableSentinelCmd": "Activate Sentry Post command", "enableTrailCmd": "Activate tracking command", "enableVioCmd": "Activate voice monitoring command", "endCall": "Outgoing call end", "enterCorrectDMRID": "Please enter the correct DMRID", "enterCorrectLandlinePhoneNumber": "Please enter the correct landline number", "enterCorrectMobilePhoneNumber": "Please enter the correct mobile phone number", "enterCorrectPhoneNumber": "Please enter the correct phone number", "example": "Example", "exportContacts": "Import address list for device programming.", "exportError": "Export data failed", "exportSuccess": "Export successful", "fence": "Fence Number", "fenceCmdSendOk": "Send GPS fence command successfully", "firstConnectTC918": "Please connect TC918 terminal or program first", "fixLastRfidTimeWarning": "The last card reading time is abnormal, is it corrected to the current card reading time?", "fixed16NumbersOrUppercaseLetters": "Fixed 16 numbers or uppercase letters", "getCtrlDataError": "Get device data failed, please log in again", "getDevDataError": "Get device data failed, please log in again", "getIotDataError": "Get iot device data failed, please log in again", "getLineDataError": "Get Patrol route data failed, please log in again", "getLinePointDataError": "Get patrol point data failed, please log in again", "getMapPointDataError": "Get marked point data failed, please log in again", "getOrgDataError": "Get organization data failed, please log in again", "getRuleDataError": "Get patrol rule data failed, please log in again", "getUserDataError": "Get user data failed, please log in again", "getUserTitleDataError": "Get job title data failed, please log in again", "gotNoRegisterCtrlData": "Received unregistered new device data", "gotNoRegisterDevData": "Received data of no login device", "gotRepeatCmd": "Received repeat data", "gpsVirSendOk": "Send GPS point command successfully", "handRoam": "Manual roaming", "highFrequency": "Frequency is too high", "importSuccess": "Imported successful", "importValidJsonFile": "Please import a valid json format file", "inTheRange": "Within range", "inputErrAndSeeHelpInfo": "Input error, please see help information", "inputNotValid": "Input of DMRID isn't valid", "insLineNotHasPoint": "Some patrol point got deleted,Please revise the corresponding patrol line", "insPointsCar": "Patrol Card", "intoAlarm": "Entering into boundary alarm", "intoGang": "Entering into boundary and back to position", "invalidIdentityInfo": "Invalid identity information", "invalidLngLat": "Invalid longitude and latitude", "ipAddress": "IP address", "isClockIn": "Checking in", "isPunchShift": "changing shift", "lastData": "Final data", "lastLocate": "Final positioning", "lastRfidTime": "Last card reading time", "licenceIsFull": "Maximum number of licenses reached", "lineData": "Patrol route data", "linePointData": "Patrol point data", "locateCmdSendOk": "Send positioning command", "locked": "Lockout", "loginExpiredAndLoginAgain": "The login session has expired, please log in again!", "loginNameUnique": "Login account repeat", "loginNetwork": "Ask for loging in network", "loginSessionExpired": "Login session has expired, please log in again or wait for automatic login", "lowFrequency": "Frequency is too low", "mapPointData": "Marked point data", "matchAnyNumber": "Match any number of any number", "matchArbitraryNo": "Match an arbitrary number", "maxLen": "Max length", "maxSelTime": "It can only check data in 3 months", "minLen": "Minuim length", "moveCtrlSendOk": "Send movement monitor command successfully", "mustBe10Len": "The key must be 10 in length", "mustBeHex": "Must be hexadecimal number", "mustBeHexChar": "Must be hexadecimal character", "mustIp": "<PERSON><PERSON>ress Only", "mustIpOrDomain": "ip or domain required", "mustLength": "Must enter {len} characters", "mustNumber": "Numbers only", "nameCannotRepeated": "Name cannot be repeated", "newCard": "New Card", "nextTimeEffect": "Setting will valid when next checking.", "noDMRID": "No dmrId,Please set firstly", "noRecords": "No log", "noResponseSatellitePositioningSwitchCommand": "No response to satellite positioning switch command", "noTarget": "Target isn't exist.", "noTerminalQuotaAvailable": "No terminal quota available", "notDynamicGroupPerm": "No dynamic group permission", "notEdit": "No edit data permissions!", "notEditSefl": "You cannot edit your own data!", "notEditUserPerm": "No permission to edit user's permission data", "notHasLinePoint": "No patrol point in patrol route, Please update patrol route.", "notHasOrg": "The selected unit doesn't exit.", "notIntercomWf": "No intercom write frequency permission", "notLinePoint": "No data of patrol poin", "notRepeaterWf": "No repeater write frequency permission", "notResAlarmCmd": "No respond for alarm setting command", "notResAreaCmd": "No respond for area searching command", "notResCenterChCtrl": "No respond to switching channel on center.", "notResClearAlarmCmd": "No respond for alarm cancel command", "notResEGChannelCmd": "No respond for emergency dispatch", "notResFenceCmd": "No respond for GPS Fence command", "notResGPSVirSet": "No respond for GPS point command", "notResLockDevCmd": "No respond for lockout command", "notResMoveCtrlCmd": "No respond for movement monitor command", "notResPositionCmd": "No respond for positioning monitor command", "notResPowerOnCmd": "No respond for unkock command", "notResSelectCmd": "No respond for checking lockout status command", "notResSentinelCmd": "No respond for GPS Fence command", "notResTextMsgCmd": "No respond for sending SMS command", "notResTrailCmd": "No respond for tracking command", "notResVioCmd": "No respond for voice monitoring command", "notSendCmd": "No command permissions are sent!", "notSetNetworkCallAgent": "Network call agent configuration is not set", "notSupportSatellitePositioning": "Does not support satellite positioning", "notVoipService": "No voice server information, no internet connection", "oldPwdError": "The old password is incorrect", "onCalling": "Calling", "openEmergencySchedule": "Open the emergency base station network scheduling", "orgData": "Organization data", "orgHasOtherData": "The unit has data, it can't be changed to virtual unit.", "orgNotHasDev": "No devices in this organization", "other": "Others", "outAlarm": "Going out boundary alarm", "outGang": "Going out boundary and leaving position", "parentNotIsSelfOrSubUnit": "The superior unit cannot be its own or subordinate unit", "phoneBookNameNo": "Repeated name, please input again", "portLimit": "address port should not greater than 65535", "postData": "Job title data", "poweredOff": "Powered down", "poweredOn": "Powered on", "processDataUpperLimitVal": "Input larger limited value please.", "processMoreRecords": "Do more data need to be processed? If not, cancel it.Otherwise input data limited items", "processMoreRecordsErrMsg": "The input is invalid.", "prochatArgsNotSet": "The ProChat gateway is incorrectly configured", "prochatDeviceIsExisted": "This prochat device is already existed", "prochatGatewayExist": "The ProChat gateway already exists", "programmingPwdError": "Programming password error", "queryCtrlHistory": "Inquire device Online log", "queryFenceCmd": "Check GPS Fence command", "queryHistoryFail": "Server is disconnected.Fail to query.", "querySatellitePositioningStatus": "Query satellite positioning status", "queryTrailCmd": "Check tracking command", "querying": "Checking...", "readAbnormal": "Abnormal data read", "readDataFailed": "Failed to read data", "readDeviceInfoFailed": "Failed to read device information", "readFileSuccessAndFaile": "read successfully: {success} file, file read failed: {fail} files", "readIdentityInfoFailed": "Failed to read identity information", "readRecordListFailed": "Failed to read recording file list", "readResourceVersionInfoFailed": "Failed to read resource version information", "readSuccess": "Read successfully", "reading": "Reading", "receiveData": "Receive data", "receiveLocateInfo": "Receive position data", "receivedSMS": "Received SMS from {sender} at {sendTime}", "repeatDMRID": "Repeated DMRID，Please input again", "repeatDevsName": "Repeated device name, Please input again", "repeatNo": "Repeated Number,Please input again", "repeatOrgShortName": "Repeated organization name，Please input again", "repeatPointRfid": "Repeated RFID，Please input again", "repeatPostName": "Repeated job title，Please input again", "repeatSipNo": "Repeated SIP number, please input again", "repeaterBusy": "Repeater channel busying", "repeaterParametersError": "There is an error in the parameters, please check the settings", "repeaterQueryFailed": "Target does not exist", "repeaterVirtualDeviceRepeatDmrId": "Repeater virtual interphone DMRID repeat", "repeaterVirtualDeviceRepeatSelfId": "Repeater virtual interphone name repeat", "repeaterWriteFail": "Repeater write frequency failed", "repeaterWriteSuccess": "Repeater write frequency success，effective after restart", "repeaterWriteSuccessEffect": "Repeater write frequency success， and takes effect immediately.", "resAlarmSetCmd": "Respond for alarm setting command", "resCb09Cmd": "Respond for remote kill and remote activation", "resCenterChannelCtrl": "Respond to switching channel command", "resChannelCtrlCmd": "Respond for channel dispatch command", "resClearLockDev": "Respond for unkock command", "resDevLockSt": "Respond for remote kill and remote activate command", "resDisListen": " Respond for receiving lockout command", "resDisSend": "Respond for transmitting lockout command", "resDisSendListen": "Respond for receiving and transmitting lockout command", "resFenceCtrlCmd": "Respond for GPS fence command", "resGpsVirCmd": "Respond for GPS point command", "resLocateCmd": "Respond for positioning command", "resMoveCtrlCmd": "Respond for movement monitor command", "resSentinelCtrlCmd": "Respond for sentry post command successfully", "resTrailCtrlCmd": "Respond for tracking command", "resVioCtrlCmd": "Respond for voice monitor command", "resetPwdError": "The two passwords are inconsistent", "resolveDmrId": "Please enter DMRID, hexadecimal starts with 0x", "resolveDmrIdErr": "Wrong length of DMR ID", "resolveDmrIdFailed": "Failed to resolve DMRID, please enter the correct DMRID", "resolveDmrIdMsg": "Input 8-digit DMR ID", "resolveDmrIdTitle": "Click to analyse DMRID", "respond": "Respond", "ruleData": "Patrol rule data", "ruleDateAlert": "End time of valid date can't less than start time", "salerId": "ID No agency ID in system setting", "satellitePositioningTurnedOff": "Satellite positioning turned off", "satellitePositioningTurnedOn": "Satellite positioning turned on", "scheduleModal1": "Fixed 4 Group Call Dispatching", "scheduleModal2": "Fixed 3 Group Call Dispatching", "scheduleModal3": "Fixed 2 Group Call Dispatching", "scheduleModal4": "Channel Group Call Dispatching", "scheduleModal5": "Base station Group Call Dispatching", "scheduleModal6": "Level 3(3rd Admin device)Group Call Dispatching", "scheduleModal7": "Level 2&3(2nd Admin device)Group Call Dispatching", "scheduleModal8": "Level 3(2nd Admin device)Group Call Dispatching", "scheduleModal9": "Level 2(2nd Admin device)Group Call Dispatching", "scheduleModalA": "Level 1&2(1st Admin device)Group Call Dispatching", "scheduleModalB": "Level 2(1st Admin device)Group Call Dispatching", "scheduleModalC": "Dynamic Group Call Dispatching", "scheduleModalD": "Level 1 Group Call Dispatching", "scheduleModalE": "System All Call Dispatching", "scheduleModalF": "Base stations network dispatching", "selAlarmCmd": "Check alarm setting command", "selError": "Checking failed", "selGPSVirCmd": "Check GPS point command", "selImgError": "Please select a picture less than 100kb", "selLinePoint": "Set patrol point", "selLockDevSt": "Check lockout status", "selMoveCtrlCmd": "Check movement monitor command", "selSentinelCmd": "Check Sentry Post command", "selSuccess": "Checking successfully", "selTimeError": "Time Error, Please set again.", "selectGpsPoint": "Select a GPS patrol point", "selectLngLat": "Set longitude and latitude", "selectTarget": "Please Select a target to send command", "selectTime": "Select time point", "sendAreaCmd": "Send area searching command", "sendClearAlarmCmd": "Send alarm cancel command", "sendError": "Send command failed", "sendPositionCmd": "Send positioning monitor command", "sendSuccess": "Command Sent", "sendTextMsgCmd": "Send SMS notification command", "sentinelCtrlSendOk": "Send sentry post command successfully", "serverReconnect": "Server has been restarted, Please reflesh again.", "serverRestartAndConnectFailed": "Disconnected from the server, automatic connection failed, please log in again", "setDisListenCmd": "Set Receiving lockout", "setDisSLCmd": "Transmitting & Receiving lockout", "setDisSendCmd": "Set Transmitting lockout", "setPriPrompt": "Confirm first or cancel Authority setting", "setupListenGroupLimit": "Set up to {limit} receiving groups per channel", "shortNoRepeated": "Short number cannot be repeated", "showActivePointAlert": "Active point is low power", "sipArgsNotSet": "The SIP gateway is incorrectly configured", "sipProtocolDevicePasswordIsNull": "The password of the SIP protocol terminal cannot be empty", "sipServerArgsNotSet": "The SIP Server gateway is incorrectly configured", "sipServerGatewayExist": "The SIP Server gateway already exists", "slotlimit": "Can not delete this slot when the slot is the last one", "smsLengthInput": "@:msgbox.smsMaxLengthInput, @:msgbox.currentCanInput {curLen} @:msgbox.character", "smsMaxLengthInput": "@:msgbox.maxLen {len} @:msgbox.character", "smsNotEmpty": "SMS content cannot be empty", "softwareDownloadFailed": "@:msgbox.downloadFailed. Unable to find software", "sortAdvice": "Suggest to sort by 10", "startMove": "Start moving", "startReading": "Start reading data", "startWriting": "Start writing data", "stopMove": "Stop moving", "success": " Successfully", "superAdminWarning": "Account which its Organization is root or empty can't send commands, Use a non-root or non-empty Organization Account please!", "switchRepeaterChannelFailed": "Failed to switch repeater channel", "switchRepeaterChannelSuccess": "Successful handover of the relay channel", "switchRepeaterFrequencyFailed": "Failed to switch repeater frequency", "switchRepeaterFrequencySuccess": "Successful switching repeater frequency", "symbolDescription": "Symbol Description", "sysIdError": "Wrong System ID", "sysSetError": "System setting failed", "sysSetSuccess": "System setting successfully", "targetInCalling": "Target in the call", "targetNotLogin": "Target is not logged in", "targetNotOnline": "Target is not online", "targetNotSelf": "The call target cannot be yourself", "telephoneNotSetChannelData": "{type} equipment does not need to set channel data", "theCardInfoOutDate": "This card reading information is out of date! Last card reading time: {lastTime}, this card reading time: {thisTime}", "timeEmpty": "Time can't be null", "trailCtrlCmdOk": "Send tracking command successfully", "tryAgainQueryChannelData": "The network is abnormal, some channel data cannot be received. Please try again later.", "turnOffSatellitePositioning": "Turn off satellite positioning", "turnOnSatellitePositioning": "Turn on satellite positioning", "unInTheRange": "Out of range", "unLocked": "Unlocked", "unableDeleteRepeaterVirtualDevice": "Unable to delete repeater virtual walkie-talkie, please try again later", "unableRequestTerminalQuota": "Unable to request terminal quota", "unknownCmd": "Unknown command", "upError": "Updated failed", "upLoadLocateInfo": "Upload positioning data", "upSuccess": "Updated successfully", "updataClientLogo": "update system logo", "updataClientTitle": "update system srolling title", "updateDeviceChannelSuccess": "Update device channel success", "updateLineDetailFaile": "Update line details failed", "updatePointType": "Patrol Point type has been updated, Please reset it.", "updateUsbServerVersion": "Please update the USB-Connector version, minimum version: {required}, current version: {curVersion}", "upgradeRepeaterVirtualDeviceFailed": "Update repeater virtual terminal failed", "upgradeSipGatewayDeviceFailed": "Update SIP gateway terminal failed", "uploadTip": "Only {type} files can be uploaded, and no more than {size}kb.", "upperLimit": "Data already reached the limit.", "usbDeviceHasBeenOut": "USB device has been pulled out", "usbDeviceInsertSuccess": "USB device inserted successfully", "useInRule": "Deleted failed,The patrol route has been refered by rule,please delete rule firstly", "userData": "User data", "userIDCard": "ID card", "validDomain": "Please enter the correct domain name", "validFileSize": "Please select a file smaller than 100k", "validHost": "Please enter the correct domain name or IP address", "validIp": "Please enter the correct IP", "validMaxNumber": "Please enter a number greater than the minimum value", "validMinNumber": "Please enter a number less than the maximum value", "vioCtrlSendOk": "Send voice monitor command successfully", "writeActiveRFIDConfigFailed": "Writing active RFID configuration failed", "writeAddressBookFailed": "Writing to address book failed", "writeAddressBookGroupFailed": "Failed to write address book group", "writeAlarmConfigFailed": "Failed to write alarm config data", "writeAnalogEmergencyAlertFailed": "Write analog emergency alert failed", "writeAttachmentSettingsFailed": "Failed to write attachment data", "writeBluetoothDataFailed": "Failed to write bluetooth setting data", "writeChannelDataFailed": "Failed to write channel data", "writeDeySettingsFailed": "Failed to write key setting data", "writeDigitalAlarmFailed": "Failed to write digital emergency alarm data", "writeDmrBasicConfigFailed": "Write DMR basic config failed", "writeEmergencyAlarmConfigFailed": "Write emergency alarm configuration failed", "writeEncryptConfigFailed": "Failed to write encrypted configuration data", "writeEncryptKeyFailed": "Failed to write key data", "writeEncryptionAES256KeyFailed": "Failed to write advanced key AES256 data", "writeEncryptionARC4KeyFailed": "Failed to write advanced key ARC4 data", "writeEncryptionARSKeyFailed": "Failed to write advanced key AES data", "writeGPIODataFailed": "Failed to write GPIO data", "writeGpsDataFailed": "Failed to write GPS data", "writeInFail": "Failed to write data", "writeInSuccess": "Successfully written", "writeLevel1ZoneFailed": "Failed to write level 1 zone data", "writeLevel2ZoneFailed": "Failed to write level 2 zone data", "writeLevel3ZoneFailed": "Failed to write level 3 zone data", "writeMenuFailed": "Writing menu data failed", "writePatrolSystemConfigFailed": "Write patrol system configuration failed", "writePhoneBookFailed": "Writing to phone book data failed", "writeProgrammingPwdFailed": "Failed to write programming password", "writeReadPasswordFailed": "Write programming read password failed", "writeReceivingGroupFailed": "Writing to the receiving group failed", "writeRegularSettingsFailed": "Write to regular settings failed", "writeRescueAndSOSChannelFailed": "Writing Rescue/SOS channel failed", "writeRescueAndSOSConfigFailed": "Writing Rescue/SOS config failed", "writeRoamConfigFailed": "Failed to write roaming config data", "writeRoamListFailed": "Failed to write roaming list data", "writeSMSFailed": "Failed to write SMS data", "writeScanConfigFailed": "Failed to write scan configuration data", "writeScanListFailed": "Failed to write scan list data", "writeSignalingSystemFailed": "Failed to write signaling system data", "writeSiteInfoFailed": "Writing to site info failed", "writeTraceMonitorConfigFailed": "Write trace monitoring configuration failed", "writeUiConfigFailed": " Write UI config failed", "writeUpsideDownConfigFailed": "Write upside down config failed", "writeValidChannelFailed": "Failed to write valid channel data", "writeVirtualClusterFailed": "Writing to virtual cluster data failed", "writeWorkAloneConfigFailed": "Write work alone config failed", "writeWritePasswordFailed": "Write programming write password failed", "writeZoneDataFailed": "Failed to write zone data"}, "nav": {"BFWebsite": "The BelFone website", "GPSpathHistory": "GPS Track", "InspectionHistory": "Patrol", "InspectionRules": "Patrol Rules", "activePatrolPointAlarm": "Active point alarm history", "activePointLowVoltageAlarm": "Active point low voltage alarm", "alarmHistory": "Alarms", "authorization": "License info", "baseStationSchedule": "B/S Control", "clientVersion": "Client version", "command": "Commands", "contacts": "Export Address List", "crudHistory": "Option history", "ctrlData": "Devices management", "data": "Basic", "enquiry": "Query", "help": "Help", "helpAbout": "About", "helpContent": "System Documentation", "home": "Home", "interphoneWriteFrequency": "Interphone write frequency", "iotDeviceHistory": "Iot Device History", "leadingInGpsData": "Import original GPS data", "lineData": "Patrol Routes", "linePointData": "Patrol Points", "mapPointData": "Map Mark", "orgData": "Organizations", "postData": "Job Titles", "readerCardHistory": "Work Shifts", "relatedSoftware": "Related software", "repeaterWriteFrequency": "Repeater write frequency", "runNotes": "System Logs", "sendCommand": "Dispatch", "serverVersion": "Server version", "smsHistory": "Send SMS history", "soundHistory": "Voice Records", "startRuningTime": "Start runing time", "switchHistory": "Power on/off", "systemLog": "system logs", "userData": "Subscribers", "version": "Version Info", "versionBuildTime": "Build time", "versionGitTag": "Git version", "phoneManage": "Phone Management", "patrolManage": "Patrol Management", "iotManage": "IoT Management", "otherOperations": "Other Operations", "phoneGatewayMapping": "Gateway Short No. Mapping", "phoneBlackWhiteList": "Phone Black/White List", "phoneDeviceAuth": "Phone Device Auth", "predefinedPhoneBook": "Predefined Phone Book", "patrolPointManage": "Patrol Point Management", "patrolRouteManage": "Patrol Route Management", "patrolRuleManage": "Patrol Rule Management", "runLog": "Run Log", "versionInfo": "Version Info", "authInfo": "Authorization Info", "logoSetting": "Logo Settings", "scrollTitleSetting": "Scroll Title Settings", "passwordChange": "Password Change", "clientDocs": "Client Docs", "controllerHistoryEvent": "Device History Event"}, "operations": {"Add": "Add", "Delete": "Delete", "Insert": "Insert", "PUpdate": "Part Update", "Update": "Update", "db_base_station": "Base station", "db_controller": "Controller", "db_controller_gateway_manage": "Controller gateway manage", "db_device": "<PERSON><PERSON>", "db_device_channel_zone": "Terminal channel zone data", "db_device_power_onoff": "Device power on/off", "db_device_register_info": "Device register info", "db_dynamic_group_detail": "Dynamic group members", "db_image": "User image", "db_iot_device": "Internet of Things Terminal", "db_line_detail": "Patrol route Details", "db_line_master": "Patrol line", "db_line_point": "Line point", "db_map_point": "Map point", "db_org": "Unit Data", "db_org_dynamic_group": "Dynamic group", "db_phone_gateway_filter": "phone black and white list", "db_phone_gateway_permission": "Phone gateway permission", "db_phone_no_list": "phone No list", "db_phone_short_no": "Phone short No", "db_rfid_rule_master": "Rule Master", "db_sys_config": "System Settings", "db_user": "User", "db_user_privelege": "User Privelege Data(Indirect operation)", "db_user_session_id": "User session", "db_user_title": "User title"}, "repeaterStatus": {"ant": "<PERSON><PERSON><PERSON> ", "antValue": "SWR", "fan": "fan ", "gps": "GPS ", "gpsStatus": "GPS status", "notInstalled": "Not Installed", "notSynced": "Not synced", "repeaterStatus": "Repeater status", "rxPll": "Receive ", "signalInterference": " Signal interference", "synced": "Synced", "temperature": "Temperature", "thereIsInterference": "There is interference", "txPll": "Transmit ", "updateTime": "Update time", "voltage": "Voltage"}, "repeaterWriteFreq": {"abnormal": "Abnormal", "hasLocate": "Has located", "locked": "Locked", "normal": "Normal", "notDetected": "not detected", "notLocate": "Not locate", "notLocked": "not locked", "repeaterDevice": "Repeater device", "repeaterModel": "Repeater model", "state": {"antStatus": "Antenna status", "fanStatus": "FanStatus", "freeRelay": "Whether to relay freely", "gpsStatus": "GPS sync status", "pllStatus": "PLL lock status", "rxStatus": "Receiving status", "sessionSlot1": "Slot 1 session status", "sessionSlot2": "Slot 2 session status", "tempStatus": "Temperature status", "txStatus": "Launch status", "volStatus": "Voltage status"}, "stateInfo": "State info", "tooHigh": "Too high", "tooLow": "Too low"}, "software": {"recordingPlayer": "Recording player", "refresh": "Refresh", "refreshFailed": "Refresh failed", "refreshSuccess": "Refresh successfully", "usbTerminalServer": "USB terminal server"}, "syncCenter": {"controller": "Synced device data", "controllerGateway": "Synced device gateway terminal", "device": "Synced terminal data", "lineDetail": "Synced inspection line details data", "lineMaster": "Synced inspections line data", "linePoint": "Synced line point data", "mapPoint": "Synced map marker data", "org": "Synced org data", "orgDeleteAndLogout": "Your unit has been deleted and you will be logged out!", "phoneGatewayFilter": "Synced phone black and white list", "phoneGatewayPermission": "Synced phone Gateway license", "phoneNoList": "Synced predefined phonebook", "phoneShortNo": "Synced phone short no.", "ruleMaster": "Synced inspections rule data", "user": "Synced user data", "userTitle": "Synced jobs data", "warning": "Warning"}, "tree": {"collapseAll": "Collapse All", "deselectAll": "Deselect All", "displayAllDev": "Display All", "expandAll": "Expand All", "filter": "Search records", "online": "Online", "quickCall": "Quick call", "selectAll": "Select All", "status": "Device Status"}, "writeFreq": {"BUSYLineOutputEffectiveLevel": "BUSY line output effective level", "ChannelAndVolume": "Channel and volume", "DefaultGroupCall": "Default group call", "Exhale": "Outbound", "GMTStandard": "GMT standard", "PTTYLineOutputEffectiveLevel": "PTT line output effective level", "SVTSiteInfo": "SVT site info", "TDMAThroughMode": "TDMA through mode", "aLocateFunc": "A (backwards + work alone alarm)", "activeSiteEnable": "Active site search", "activeSiteRoamingEnable": "Active site roaming enable", "activeSiteSearch": "Active site search", "activeSiteSearchTiming": " Active site search timing (s)", "addArea": "Add area", "addChannel": "Add channel", "addressBookGroup": "Address book group", "addressGroup": {"selectedContact": "The selected contact", "ungroupedContact": "Ungrouped contact"}, "advanced": "Advanced", "advancedEncryptionAES256": "Advanced encryption AES256", "advancedEncryptionARC4": "Advanced encryption ARC4", "advancedEncryptionARS": "Advanced encryption AES", "aes256List": "AES256 list", "airAuthKey": "Air authentication key", "airEncryption": "Air encryption", "alarmAndWhistle": "Alarm and whistle", "alarmAutoSendGps": "The alarm auto sends GPS messages", "alarmSetting": "Alarm setting", "alarmSquelchMode": "Alarm squelch mode", "alarmTone": " Alarm tone", "alias": "<PERSON><PERSON>", "aliasDisp": "Alias display", "aliasEdit": "<PERSON><PERSON>", "aliasMenu": "Alias menu", "allChannelDetection": "All channel detection", "allIndicators": "All indicators", "allow": "Allow", "allowDeleteAllRecord": "Allow recording files to be erased", "allowErasingDevice": "Allow erasing device", "allowShake": "Allow vibration", "allowedSelfDestruct": "Allow self-destruct", "always": "Always", "analogAlert": "Analog alert", "analogAllowed": "Analog allowed", "analogCallHangTime": "Analog call hang time (s)", "analogChannel": "Analog channel", "analogCompatibleDigital": "Analog compatible digital", "analogEmergencyAlarm": "Analog emergency alarm", "analogEmergencyAlertSystem": "Analog emergency alert system", "arc4List": "ARC4 list", "areaId": "Area ID", "areaList": "Area list", "areaNoCannotRepeated": "The serial number under the same area cannot be repeated", "armAlarmKey": "Shoulder microphone alarm", "authentication": "Authentication", "authenticationSecretKey": "Authentication secret key", "auto": "automatic", "autoBackLightTime": "Backlight auto time", "autoEmergencyCall": "Automatic emergency call", "autoKeyboardLock": "Auto keyboard lock", "autoKeyboardLockDelayTime": "Auto keyboard lock on time delay", "autoRoamSearchTime": "Auto roaming search time (s)", "autoRoaming": "Auto roaming", "autoRoamingSearchInterval": "Auto roaming search interval (s)", "autoScanning": "Auto scanning", "autoSiteSearchTimer": "Auto search timer", "availableChannel": "Available channel", "back2Back": "Back to back", "back2BackMode": "Back to back forwarding mode", "backToBackEnable": "Back to back switch", "backgroundPromptTone": "Background prompt tone", "backlight": "Backlight", "backlightSettings": "Backlight settings", "backwardsTriggered": "Backwards triggered", "base": "Base", "baseSettings": "Base settings", "basedOnChannelDefaultGroupCall": "Based on channel default group call", "batchDownload": "Btach download", "batteryCharge": "Battery charge", "batteryInfo": "Battery information", "bdsLocate": "Beidou locating", "beatFreq": "Beat frequency", "beidou": "<PERSON><PERSON><PERSON>", "bluetooth": "Bluetooth", "bluetoothAudio": "Bluetooth audio", "bluetoothConnectManagement": "Bluetooth connection management", "bluetoothFunc": "Bluetooth function", "bluetoothInfo": "Bluetooth info", "bluetoothMode": "Bluetooth mode", "bluetoothOption": "Bluetooth option", "bluetoothPTTKeep": "Bluetooth PTT Keep", "bluetoothRecordingChooseOne": "Bluetooth option and recording option can only choose one", "bluetoothSettings": "Bluetooth settings", "bluetoothSwitch": "Bluetooth Switch", "bootInterfaceDisp": "Boot interface display", "broadBand": "Broad band", "busyChannelLock": "Busy channel lock", "button": "<PERSON><PERSON>", "buttonTone": "Key tone", "callDir": "Voice direction", "callDirectionEnable": "Call direction information", "callDisplayMode": "Call Display Mode", "callEmissionPermitConditions": "Call emission permit conditions", "callHangsLed": "Call hang LED", "callMode": "Call mode", "callOut": "Call out", "callOutTone": "Call out tone", "callPrompt": "Call prompt tone", "callPromptTimes": "Number of call reminders", "callToneDecode": "Call prompt decode", "callToneVibration": "Call tone vibration", "callingEndTone": "Call end tone", "carrier": "Carrier", "carrierSquelchLevel": "Carrier squelch level", "cdcss": "CDCSS", "cdcssInvert": "CDCSS Invert", "chAreaChannelAsScanList": "Channel area channel as scanning list", "chTimeSlotCalibrator": "Channel time slot calibrator", "channelBroadcastSound": "Channel broadcast sound", "channelConfig": "Channel config", "channelDataFormValidate": "Channel data verification failed", "channelLock": "Channel lock", "channelNotSetArea": "There are unset areas of channels", "channelNumber": "Channel number", "chooseAnnouncer": "<PERSON>ose an announcer", "clearRecord": "Record clear", "clearSms": "Clear SMS messages", "closePosition": "Close position", "companding": "Companding", "compandingEnable": "Companding enable", "configName": "List name", "confirmedDataSingleCall": "Confirmed data single call", "connectionTimes": "Connection times", "contactAlias": "Contact alias", "contactAliasAndId": "Contact alias and ID", "contactDelete": "Contact Delete", "contactGroup": "Contact Group", "contactId": "Contact ID", "containedChannel": "Contained channel", "controlBusiness": "Control business", "controlCenter": "Control center", "correctChDataTip": "{name} has channel data with errors, please correct", "ctcss": "CTCSS", "ctcssCdcss": "CTCSS/CDCSS", "currentSlot": "Current time slot", "currentVolume": "Current volume", "customKey": "Key {name}", "dataCallConfirm": "Data call confirm", "dataCompression": "Data compression", "dateTime": {"date": "Date", "hours": "Hours", "minutes": "Minutes", "month": "Month", "year": "Year"}, "deEmphasisAndPreEmphasis": "De-emphasis and Pre-emphasis", "decoding": "Decoding", "defaultChannel": {"ch1": "Preset channel 1", "ch2": "Preset channel 2", "ch3": "Preset channel 3", "ch4": "Preset channel 4"}, "defaultKeyboardInput": "Default keyboard input", "defaultKnobFunc": "Default knob function", "defaultPriCall": "Default private call", "defaultTxChNetwork": "Default transmit channel network", "deleteGroup": "Delete group", "denoise": "<PERSON><PERSON>", "denoiseEnable": "<PERSON><PERSON>", "device": "<PERSON><PERSON>", "deviceDetectDecode": "Interphone detection decoding", "deviceRemoteDeadDecode": "Device remote dead decoding", "deviceRemoteDestroy": "Device remote destroy", "deviceStun": "Interphone stun", "deviceWokeUp": "Interphone woke up", "digitalAlarmFormValidate": "Digital alarm data verification failed", "digitalAlert": "Digital alert", "digitalAllowed": "Digital allowed", "digitalAnalogChannel": "Digital Analog channel", "digitalChannel": "Digital channel", "digitalCompatibleAnalog": "Digital compatible analog", "digitalEmergencyAlarm": "Digital emergency alarm", "digitalEmergencyAlertSystem": "Digital emergency alert system", "digitalMode": "Digital mode", "disable": "Disable", "displayName": "Display name", "displayNameAndNumber": "Show name and number", "displayNameNumber": "Display name or alias", "displayNumber": "Display number", "dmrBaseSettings": "DMR base settings", "dualTimeSlot": "Dual time slot", "duplex": "Duplex", "duplexMode": "Duplex mode", "dynamicKey": "Dynamic key", "early": "Early", "edit": "Edit", "editContact": "Contact Edit", "editGroup": "EditGroup", "editList": "Edit list", "emergencyAlarmAndCall": "Emergency alarm and call", "emergencyAlarmAndVoice": "Emergency alarm and voice", "emergencyAlarmConfirm": "Emergency alarm confirm", "emergencyAlarmExitDuration": "Emergency alarm long press exit duration", "emergencyAlarmIndication": "Emergency alarm indication", "emergencyAlarmSystem": "Emergency alarm system", "emergencyAlertDuration": "Emergency alert duration", "emergencyAlertMessage": "Emergency alert message", "emergencyAlertSystem": "Digital/Analog emergency alert system", "emergencyAlertTimes": "Number of emergency alerts", "emergencyCallAlert": "Emergency call alert", "emergencyCallTimes": "Number of emergency calls", "emergencySystem": "Emergency system", "emittingLed": "Emission LED", "emphasis": "Emphasis", "enable": "Enable", "enableAuth": "Enable authentication", "encoding": "Eencoding", "encryptType": "Encryption Type", "encryptedList": "Encrypted list", "encryption": "Encryption", "encryptionAlgorithm": "Encryption algorithm", "encryptionConfig": "Encryption config", "encryptionKey": "Encryption key", "enhancedXor": "Enhanced exclusive or", "entryDelay": "Entry delay (s)", "exitDelay": "Exit delay (s)", "femaleVoice1": "Female Voice 1", "femaleVoice2": "Female voice 2", "fieldStrengthInfo": "field strength information", "fileName": "File name", "firstChoice": "First choice", "firstPriorityChannel": "First priority channel", "flashlightTime": "Interval flashing time(ms)", "followMainSiteSettings": "Follow the main site settings", "forbid": "Forbid", "forward": "Forward", "freqOffset": "Frequency offset (MHz)", "freqRepeated": "<PERSON><PERSON><PERSON><PERSON> repeated", "frequencyRangeError": "Frequency range error", "fullList": "The list is full, add failed!", "gLocateFunc": "G(location function)", "galileo": "Galileo", "generalSettingsFormValidate": "General Settings data validation failed", "getRecordList": "Get recording list", "girlVoice": "Girl voice", "glonass": "GLONASS", "gpioSettings": "GPIO", "gpsAndBeidou": "GPS+Beidou", "gpsEnable": "Enable positioning function", "gpsLocate": "Satellite locating", "gpsMode": "model", "gpsUpdateTime": "GPS update time (sec)", "groupCallTone": "Group call tone", "groupJoinOrExit": "Join or exit the group", "groupManagement": "Group Management", "highLevel": "High level", "highPerformanceMode": "High performance mode", "highPowerSosTime": "High power for sending interval(min)", "hourDiff": "Clock difference", "idAlreadyExits": "The ID already exists, please re-enter", "imagePreview": "Image Preview", "inOutNetworkTone": "In-network and out-of-network tone", "inbound": "Inbound", "incomingCall": "Incoming call", "independentSettings": "Independent settings", "indicationTones": "Indication tones", "indicatorSettings": "Indicator settings", "interphoneActivation": "Interphone activation", "interphoneConfig": "Interphone configuration", "interphoneDetection": "Interphone detection", "interphoneInfo": "Interphone info", "interphoneRemoteKill": "Interphone remote kill", "intervalHonkingTime": "Interval honking time(s)", "ipSiteConnection": "IP site connection", "key01": "Key 01", "key02": "Key 02", "keyList": "Key list", "keyP": "Key P", "keyboardHangTime": "Keyboard backlight hang time (s)", "keyboardLock": "Keyboard lock", "keys": "Key {key}", "lLocateFunc": "L(recording function)", "langEnv": "Language environment", "lastActiveChannel": "Last active channel", "lastActivityChannel": "The last activity channel", "late": "Late", "lcdHangTime": "LCD backlight hang time (s)", "lgLocateFunc": "LG(location + recording)", "localCall": "Local call", "localEmergencyAlert": "Local emergency alert", "localEmergencyHonk": "Local Emergency Honk", "locateFunc": "Locate function", "locateMode": "Locate mode", "locationEnable": "Position switch", "locationSystem": "Positioning system", "lockKeys": {"backKey": "Back key", "channelKey": "Channel knob", "confirmKey": "Confirm key", "dialKey": "Dial key", "downKey": "Down arrow", "f1key": "F1 key", "f2key": "F2 key", "f3key": "F3 key", "f4key": "F4 key", "f5key": "F5 key", "func1Key": "Function key 1", "func2Key": "Function key 2", "knobKey": "Knob key", "leftKey": "left key", "offKey": "Off-hook key", "onHookKey": "Hang up key", "orangeKey": "Orange key", "p1Key": "P1 key", "p2Key": "P2 key", "pttKey": "PTT", "rightKey": "right key", "upKey": "Up arrow", "volumeKey": "Volume knob"}, "longPressNumCallTable": "<PERSON> press the numeric keyboard function to call", "lookOver": "Look over", "lowBatteryLed": "Low battery LED", "lowLevel": "Low level", "lowPowerSosTime": "Low power for sending interval(min)", "lowVoltageAlarmTone": "Low-voltage alarm tone", "maleVoice1": "Male voice 1", "maleVoice2": "Male voice 2", "manualQuickCallType": "Manual shortcut call", "manualSiteRoam": "Manual site roaming", "mapping": "Mapping", "maxChannelLimit": "{zoneTitle} can configure up to {count} channels", "maydayRescue": "Mayday/Rescue setting", "mediumPowerFunc": "Medium Power", "memoryInfo": "memory information", "messageTone": "SMS tone", "messageVibration": "SMS vibration", "micActiveTime": "Microphone activation time (s)", "middlePowerSosTime": "Medium power for sending interval(min)", "minuteDiff": "Minute difference", "missedCall": "Missed call", "modelDataError": "Model data error", "monitorSquelchMode": "Monitor squelch mode", "motionDetectionOnly": "Motion detection only", "multiKeyDecryption": "Multi-key decryption", "narrowBand": "Narrow band", "navType": "Navigation mode", "networking": "Networking", "networkingMode": "Networking mode", "newGroup": "New group", "newGroupContact": "New group call", "newSecretKey": "New secret key", "newSingleContact": "New Single Call", "noSubtone": "No subtone", "nonPriorityChannelDetection": "Non-priority channel detection", "nonstandardPhase": "Nonstandard phase", "normal": "Normal", "normallyOpen": "Normally open", "notHaveValidChannel": "The device does not have a valid channel. Please configure the channel data first", "notPoliteRetry": "Not polite to retry", "numberKeyFastDial": "Number Key Fast Dial", "numericKeyboard": " Numeric keyboard {key}", "off": "Off", "on": "On", "oneTouchCall": "One touch call", "oneTouchKey": "One touch call {key}", "onlyChannel": "Only channel", "onlyEmergencyVoice": "Emergency calls only", "onlyGroupCall": "Group call only", "onlySingleCall": "Single call only", "onlyStill": "Only still", "onlyTilt": "Only tilt", "onlyVolume": "Only volume", "onlyWhistle": "Only whistle", "optionalFeatures": "Optional Features", "optionalLockKey": "Optional lock key", "orangeButton": "Orange button", "ownGroup": "Owning Group", "passwordErrorNumber": "Number of power on password errors", "paste": "Paste", "patrol": "Patrol", "patrolClockIn": "Patrol clock in", "patrolRecord": "Patrol records", "patrolSweepCard": "Patrol sweep card", "phoneBook": "Phone book", "phoneContact": "Phone contact", "phoneDialer": "Phone dialer", "politeRetry": "Polite to retry", "powerAutoConversion": "Power automatic conversion", "powerLevel": "Power level", "powerOnPwdRange": "Boot password input range: 0-6", "powerSavingMode": "Power saving mode", "poweredTone": " Power on/off tone", "presetChannel": "Preset channel", "priorityChannelDetection": "Priority channel detection", "priorityChannelTone": "Priority channel tone", "priorityInterrupt": "Priority interrupt", "productionInfo": "Production Information", "promptTimeBackwards": "Prompt time backwards (s)", "pttAlone": "Whether PTT is independent", "pttTimes": "PTT times", "queryByCallDir": "Query by voice direction", "queryById": "Query by target ID", "queryByTime": "Query by time", "queryCommand": "Query command", "quickSearch": "Quick Search", "randomKey": "Random key", "randomKeyEncryption": "Random key encryption", "randomizedAlgorithm": "Random algorithm", "readPassword": "Program read password", "realTime": "Real time", "receiveDuration": "Receive duration", "receiveLed": "Receive LED", "receiveSquelchMode": "Receive squelch mode", "receiverAlarmTone": "Receiver alarm tone", "recordFile": "Recording file", "recordId": "Record ID", "recordList": "Recording List", "recordPlayback": "Recording playback", "recordSetting": "Recording Settings", "recordSwitch": "Recording switch", "recordTime": "Duration", "recordingFunc": "Recording function", "recordingOption": "Recording option", "rejectStrangerCall": "Reject stranger call", "remoteAlertCount": "Number of remote alerts", "remoteDestructionDecode": "Remote destruction decoding", "remoteDetectionDecode": "Remote detection decoding", "remoteEraseDecodeEnable": "Remotely destroy air interface encryption enable", "remoteKillActivateDecode": "Remote kill/activate decode", "remoteKillActivateDecodeAuth": "Remote kill/activate decode authentication", "remoteMonitorAuth": "Remote monitoring authentication", "remoteMonitorDecode": "Remote monitoring decoding", "remoteMonitorDecodeEnable": "Remote monitoring air interface encryption enable", "remoteMonitorDuration": "Remote monitoring duration", "remotePromptDecode": "Remote prompt decoding", "remoteShutEncryptEnable": "Walkie-talkie remote shutdown/wakeup air interface encryption enable", "remoteStunDecodeEnable": "Remote remote stun/remote wake up air interface encryption enable", "remoteStunWakeupDecode": "Remote stun/wakeup decode", "reply": "Reply", "replyChannel": "Reply channel", "rescueScanHandUpTime": "Rescue <PERSON>an Suspend Time (ms)", "resend": "Resend", "residenceTime": "Residence time", "resourceVersion": "Resource version information", "ringToneSettings": "Ring tone settings", "roamEnable": "Roam on/off", "roamList": "Roam list", "roamLockSite": "Roam site lock", "roamManual": "Manual site roam", "roamSettings": "Roam settings", "roaming": "Roaming", "roamingGroup": "Roaming group", "rssiDetectCycle": " RSSI detection cycle (s)", "runBackward": "Run backward", "runBackwardAlarm": "Run backward alarm", "rxAnalogCdcss": "Receive analog CDCSS", "rxCdcssType": "Receive CTCSS/CDCSS type", "rxDigitalCdcss": "Receive digital CDCSS", "rxGroupExceedLimit": "The maximum number of receiving groups is {max}, which is currently over {n}", "sLocateFunc": "S(full function)", "sameLaunchPermCondition": "Same as launch permission conditions", "samePowerPassword": "Same power-on password", "satellitePositionInfo": "Satellite positioning position info", "satellitePositionSwitch": "Satellite positioning switch", "saveModeOnTimeDelay": "The power saving mode on time delay", "savePowerDelayTime": "Power saving delay start time", "scan": "<PERSON><PERSON>", "scanAndRoamList": "Scan / Roam list", "scanEdit": "<PERSON>an edit", "scanGroupFormValidate": "Scan group data verification failed", "scanList": "Scan list", "scanOrRoamList": "Scan or roam list", "scanRoamStatusLed": "Scan / roaming status LED", "scanSamplingTime": "Scan sampling time (ms)", "scanSettings": "Scan settings", "scanningEmissionMode": "Scanning emission mode", "scanningGroup": "Scanning group", "scramble": "Scramble", "secondPriorityChannel": "Second priority channel", "secretKeyName": "Secret key name", "secretKeySwitch": "secret key switch", "secretKeyValue": "Secret key value", "selectTargetModel": "Please select to create models", "setDateTime": "Set time and date", "setRealTime": "Set real time", "shake": "Shake", "shakeMode": "Shake mode", "shiNeng": "Enable", "shieldedHeadphones": "Shielded headphones", "shieldedKeyboardLock": "Stealth mode shield keyboard lock", "shieldedLedLight": "Stealth Mode muting lamps", "shieldedMicrophone": "Shielded microphone", "showContactContent": "Show contact content", "showStrangeNumber": "Show strange numbers", "signalingPassword": "Signaling password", "signalingPwd": "Air interface key", "signalingSystem": "Signaling system", "signalingType": "Signaling type", "silenceAndVoice": "Silence and voice", "silenceCarryVoice": "Silence carry voice", "simplex": "Simplex", "singleCallTone": "Single call tone", "singleCallVibration": "Single call vibration", "singleCallVoiceConfirm": "Single call voice confirm", "singleKeyFuncCall": "Single key function call", "siteInfo": "Site Info", "siteLock": "Site lock", "siteSearchTimer": "Site search timer (s)", "sms": "SMS", "smsUdpCompress": "SMS UDP header compression", "softKeyCallType": {"GROUP": "Group call", "MSG": "SMS", "SINGLE": "Single call", "TIP": "Call reminder"}, "softKeyFuncDefine": {"AGING_FUNC_TEST_ON_OFF": "Aging function test on/off", "A_D_SWITCH": "Digital analog switch", "ActiveRFIDPRead": "Active RFID read card", "AllMuteOnOff": "All mute on/off", "BACKLIGNT": "Backlight auto on/off", "BACKLIGNT_SWITCH": "Backlight auto turns on/off", "BACKTOHOME": "Back to Home page", "BASEBAND_FIRMWARE": "Baseband firmware upgrade", "BATTERY_CHANGE": "Battery level prompt", "BATTERY_CHARGE": "Battery tips", "BLUETOOTH_SEARCH_AUTO_CONNECT": "Bluetooth search and auto connect", "Back2BackForwardingMode": "Back to back forwarding mode", "BackToBackEnable": "Back to back switch", "BluetoothEnable": "Bluetooth switch", "CALL_RECORDS": "Call log", "CALL_TONE": "Call prompt", "CH_DOWN": "Channel down", "CH_LOCK_SW": "Channel lock", "CH_PRESET1": "Default channel 1", "CH_PRESET2": "Default channel 2", "CH_PRESET3": "Default channel 3", "CH_PRESET4": "Default channel 4", "CH_UP": "Channel up", "COMMON_CONTACT_LIST": "Common contacts list", "CONTACTS": "Address book", "CONTAS_LIST_MENU": "Contact list", "CONTAS_MENU": "Address book", "DATE_TIME": "Current date prompt", "DEL_INVALID_CHL": "Delete useless channel", "DEL_USELESS_CH": "Useless channel deletion", "DEV_ACTIVE": "Interphone activation", "DEV_DETECT": "Interphone detection", "DEV_DIE": "Interphone remote dead", "DEV_RUIN": "Interphone remote destroyed", "DEV_STUN": "Interphone stun", "DEV_WAKEUP": "Interphone woke up", "DISCONNECT": "Priority interrupt", "DTA": "Digital/Analog conversion", "DTMF_MENU": "DTMF keyboard", "ENCRYPTION": "Encryption", "ERRNUMBER_RX": "Bit error rate test - receive", "ERRNUMBER_TX": "Bit error rate test - launch", "EncryptionSwitch": "Encryption switch", "GLARE_FLASHLIGHT": "Strong light flashlight on/off", "GPS_ON_OFF": " Satellite positioning on/off", "GPS_POSITION_SWITCH": "Commutateur de positionnement satellite", "GPS_SWITCH": "Satellite positioning data upload", "HANG_UP": "Hang up", "HIDE_SWITCH": "Stealth mode on/off", "INT_PRI": "Priority interrupt", "KEYBOARD_LOCK": "Keyboard lock", "KEYLOCK": "Keyboard lock", "LONGMONI": "Permanent monitoring", "MAIN_MENU": "Open main menu", "MANUAL_DIAL": "Manual dial", "MANUAL_ROAMING_LOCK": "Manual roaming lock", "MENU_INTERFACE": "Menu interface", "MONI": "Monitor", "MONITOR_ON_OFF": "Listen", "MSG_MENU": "SMS", "NETMODE_CHG": "Repeater/offline", "NONE": "Not set", "NetworkingOnOff": "Networking On / Off", "OFF_NETWORK_ON_OFF": "Off net on/off", "PHONE_BOOK": "Phone book", "PHONE_LIST_MENU": "Telephone contact list", "PRIORITY_INTERRUPT": "Priority interruption", "PWRMODE_CHG": "High/low power", "PWRMODE_CHG_BP660": "power switching", "PassiveRFIDRead": "Passive RFID read card", "RECORDING_ON_OFF": "Recording on/off", "RECORD_SWITCH": "Recording on/off", "RESCUE_SCAN_SWITCH": "Rescue scan mode on/off", "RFIDQuery": "RFID backlog query", "RFID_REGISTER": "RFID registration", "RMT_MONITOR": "Remote monitor", "ROAM_MANUAL": "Manual site roaming", "ROAM_SWITCH": "Roam on/off", "SCALL_DIAL": "Single call manual dial", "SCALL_DIAL_MENU": "Manual dial interface", "SCAN": "Scan on/off", "SCAN_SWITCH": "Scan on/off", "SITE_LOCK_SWITCH": "Site lock switch", "SK_BT_SEARCH_AUTO_CONNECT": "Bluetooth search and automatic connection", "SK_BT_SWITCH": "Bluetooth on/off", "SK_CH_LOCK": "Channel lock on/off", "SK_DISABLED_ALL_LED": "Disable all LEDs", "SK_DNS_SWITCH": "Noise reduction on/off", "SK_ENCRYPTION_SWITCH": "Encryption on/off", "SK_VIBRATION_SWITCH": "Vibration on/off", "SMS": "SMS", "SPDCALL1": "Single key function call 1", "SPDCALL2": "Single key function call 2", "SPDCALL3": "Single key function call 3", "SPDCALL4": "Single key function call 4", "SPDCALL5": "Single key function call 5", "SPDCALL6": "Single key function call 6", "SQUELCH_LEVEL_ADJUST": "Noise level adjustment", "SQUELCH_OPEN": "Quiet on", "STANDBY_INTERFACE": "Standby interface", "STEALTH_MODE_ON_OFF": "Stealth mode on/off", "SystemStatusQuery": "System status query", "TELEMETRY_BUTTON1": "Telemetry button 1", "TELEMETRY_BUTTON2": "Telemetry button 2", "TELEMETRY_BUTTON3": "Telemetry button 3", "TONE_MUTE": "All tone switches", "TONE_MUTE_SWITCH": "All prompt tone on/off", "TOP_CONTAS_LIST_MENU": "Commonly used contact list", "TRANSIENT_MONITOR": "Transient monitoring", "TRANSIENT_SQUELCH_OPEN": "Transient noise on", "TRANSMIT_POWER_HIGH_LOW": "Transmit power high/low", "TX_TEST": "Launch test", "VIBRATION_SETTINGS": "Vibration settings", "VOLUME_UP": "Volume up", "VOLUMN_DOWN": "Volume down", "VOX": "Voice control switch", "VOX_SWITCH": "Voice on/off", "WARNING_OFF": "Emergency mode off", "WARNING_ON": "Emergency mode on", "WORK_ALONE_SWITCH": "Separate operating switch", "WORK_DOWN_SWITCH": "Upside down switch", "ZONE_DOWN": "Region down", "ZONE_SWITCH": "Zone switch", "ZONE_UP": "Region up"}, "sosInfo": "SOS info", "sosRescue": "SOS/Rescue", "sosRescueCfg": "SOS/Rescue config", "sosRescueChannel": "SOS/Rescue channel", "specifiedChannel": "The specified channel", "specifyTransmitChannel": "Specify transmit channel", "specifyTxTimeSlot": "Specify transmit time slot", "speechRate": "Speech rate", "standardPhase": "Standard phase", "stealthMode": "Stealth mode", "stealthModeBacklight": "Stealth mode backlight", "stealthModeHeadsetMute": "Stealth mode headset mute", "stealthModeSettings": "Stealth mode settings", "stealthModeVibrationShield": "Stealth mode vibration shield", "strengthen": "Strengthen", "subtoneNotDetected": "Subtone is not detected", "subtoneScanningMode": "Subtone scanning mode", "svtChannelList": "Cross-site roaming list", "switchChannelSquelchMode": "Switch channel squelch mode", "switchHighPowerThreshold": "Switch to high power threshold (dBm)", "switchLowPowerThreshold": "Switch to low power threshold (dBm)", "switchMediumPowerThreshold": "Switch to medium power threshold (dBm)", "syncTimezone": "Sync time zone", "synchronisedTime": "Synchronised time", "system": "System", "systemFunction": "System function", "systemInfo": "System info", "targetId": "Target ID", "terminalType": "Terminal type", "theAlarm": "The alarm", "theSelected": "The selected", "tiltOrMotionDetection": "Tilt or motion detection", "tiltOrStill": "Tilt or standstill", "timeSlotSelection": "Time slot selection", "timeZoneId": "Time zone ID", "transmitAlertAudioDuration": "Transmit alert audio duration (s)", "transmitAlertAudioTimes": "Number of transmit alert audio", "triggerControl": "Trigger control", "triggerInclination": "Trigger inclination (deg)", "triggerMode": "Trigger mode", "ttsSpeed": "Speech speed", "ttsTune": "<PERSON><PERSON>", "ttsVolume": "Volume", "txAnalogCdcss": "Transmit analog CDCSS", "txCdcssType": "Transmit CTCSS/CDCSS type", "txDigitalCdcss": "Transmit digital CDCSS", "uiSettings": "UI settings", "uiVersion": "UI version", "unConfirmSingleCall": "Unconfirmed Single Call SMS", "underAlarmRemoteMonitorDecode": "Remote monitoring decode under emergency alarm", "unlimited": "Unlimited", "unlimited2": "unlimited", "upsideDown": "Upside down", "upsideDownSwitch": "Upside down switch", "urgentRemoteMonitorDecode": "Emergency remote monitor decoding", "vibration": "Vibration", "vibrationSettings": "Vibration settings", "viewRecordingFiles": "View recording files", "virtualCluster": "Virtual cluster", "virtualClusterFormValidate": "Virtual cluster data validation failed", "voiceBroadcast": "Voice broadcast", "voiceBroadcastSettings": "Voice broadcast settings", "voiceCallEmbedAlias": "Voice call embed alias", "voiceCallEmbedding": "Voice call embedding location information", "voiceDuplex": "Voice Duplex", "voiceEndTone": "End tone of voice", "voiceLaunch": "Voice launch", "voicePriority": "Voice priority", "voicePrompt": "Voice prompt", "weightingMark": "Weighting mark", "workAlone": "Work alone", "workAloneAlarm": "Work alone alarm", "workAloneReminderTime": "Work alone reminder time (s)", "workResOptAlone": "Work response operation alone", "workResTimeAlone": "Work response time alone (m)", "writePassword": "Program write password", "xor": "Exclusive or", "zone": "Zone", "zoneConfig": "New area list", "zones": {"leaf": "Zone of level 3", "parent": "Zone of level 2", "root": "Zone of level 1"}}}